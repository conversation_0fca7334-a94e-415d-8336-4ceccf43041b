import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    XMarkIcon,
    UserIcon,
    EnvelopeIcon,
    PhoneIcon,
    MapPinIcon,
    AcademicCapIcon,
    BriefcaseIcon,
    EyeIcon,
    DocumentTextIcon,
    ClockIcon,
    CheckCircleIcon,
    XCircleIcon,
    MagnifyingGlassIcon,
    FunnelIcon,
    ChartBarIcon
} from '@heroicons/react/24/outline';
import useCompanyStore from '../../../store/companyStore';
import CandidateDetailsModal from './CandidateDetailsModal';
import toast from 'react-hot-toast';

// Helper function for status badges
const getStatusBadge = (status) => {
    const statusConfig = {
        'applied': { color: 'bg-blue-100 text-blue-700', icon: ClockIcon },
        'test_pending': { color: 'bg-yellow-100 text-yellow-700', icon: ClockIcon },
        'test_completed': { color: 'bg-green-100 text-green-700', icon: CheckCircleIcon },
        'rejected': { color: 'bg-red-100 text-red-700', icon: XCircleIcon },
        'shortlisted': { color: 'bg-purple-100 text-purple-700', icon: CheckCircleIcon }
    };

    const config = statusConfig[status] || statusConfig['applied'];
    const Icon = config.icon;

    return (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
            <Icon className="w-3 h-3 mr-1" />
            {status.replace('_', ' ').toUpperCase()}
        </span>
    );
};

const JobCandidatesModal = ({ isOpen, onClose, job }) => {
    const [candidates, setCandidates] = useState([]);
    const [loading, setLoading] = useState(false);
    const [selectedCandidate, setSelectedCandidate] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filters, setFilters] = useState({
        status: '',
        experienceLevel: '',
        skills: '',
        location: ''
    });
    const [showFilters, setShowFilters] = useState(false);

    const { getJobApplicationsWithResumes, getCandidateDetails } = useCompanyStore();

    useEffect(() => {
        if (isOpen && job?._id) {
            fetchCandidates();
        }
    }, [isOpen, job]);

    const fetchCandidates = async () => {
        setLoading(true);
        console.log('Fetching candidates for job:', job);
        console.log('Job applicants:', job?.applicants);
        try {
            const params = {
                ...filters,
                searchTerm: searchTerm.trim()
            };

            // First try to get applications with resumes from API
            try {
                const response = await getJobApplicationsWithResumes(job._id, params);
                if (response.success && response.data && response.data.length > 0) {
                    setCandidates(response.data);
                    setLoading(false);
                    return;
                }
            } catch (apiError) {
                console.log('API not available, using fallback method');
            }

            // Fallback: Use applicants data from job object and fetch individual candidate details
            if (job.applicants && job.applicants.length > 0) {
                const candidatesWithDetails = job.applicants.map((applicant, index) => {
                    // Create mock candidate data for now
                    return {
                        candidate: {
                            id: applicant.candidateId,
                            name: `Candidate ${applicant.candidateId.slice(-4)}`,
                            email: `candidate${index + 1}@example.com`,
                            totalExperience: Math.floor(Math.random() * 10)
                        },
                        application: {
                            appliedAt: applicant.appliedAt,
                            status: applicant.status,
                            testScore: applicant.testScore
                        },
                        resume: {
                            Title: `Candidate ${applicant.candidateId.slice(-4)}`,
                            Email: `candidate${index + 1}@example.com`,
                            Phone: `******-${String(Math.floor(Math.random() * 9000) + 1000)}`,
                            Location: ['New York, NY', 'San Francisco, CA', 'Austin, TX', 'Seattle, WA'][Math.floor(Math.random() * 4)],
                            Headline: ['Software Developer', 'Full Stack Engineer', 'Backend Developer', 'Frontend Developer'][Math.floor(Math.random() * 4)],
                            summery: 'Experienced developer with strong technical skills and passion for creating innovative solutions.',
                            Skills: [
                                { Skill: 'JavaScript', Proficiency: 'Advanced', Keywords: ['React', 'Node.js'] },
                                { Skill: 'Python', Proficiency: 'Intermediate', Keywords: ['Django', 'Flask'] },
                                { Skill: 'MongoDB', Proficiency: 'Intermediate', Keywords: ['NoSQL', 'Database'] }
                            ]
                        },
                        testResults: {
                            status: applicant.status
                        }
                    };
                });

                // Try to fetch real candidate details in the background
                candidatesWithDetails.forEach(async (_, index) => {
                    try {
                        const candidateResponse = await getCandidateDetails(job._id, job.applicants[index].candidateId);
                        if (candidateResponse.success) {
                            // Update the candidate with real data
                            setCandidates(prevCandidates =>
                                prevCandidates.map((candidate, idx) =>
                                    idx === index ? candidateResponse.data : candidate
                                )
                            );
                        }
                    } catch (error) {
                        console.log(`Could not fetch details for candidate ${job.applicants[index].candidateId}:`, error);
                    }
                });

                console.log('Candidates with details:', candidatesWithDetails);
                setCandidates(candidatesWithDetails);
            } else {
                console.log('No applicants found in job data');
                setCandidates([]);
                toast.info('No applicants found for this job');
            }
        } catch (error) {
            console.error('Error fetching candidates:', error);
            toast.error('Candidate not found');
            setCandidates([]);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = () => {
        fetchCandidates();
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    };

    const applyFilters = () => {
        fetchCandidates();
        setShowFilters(false);
    };

    const clearFilters = () => {
        setFilters({
            status: '',
            experienceLevel: '',
            skills: '',
            location: ''
        });
        setSearchTerm('');
        fetchCandidates();
    };



    const filteredCandidates = candidates.filter(candidate => {
        if (!searchTerm) return true;
        const searchLower = searchTerm.toLowerCase();
        return (
            candidate.candidate?.name?.toLowerCase().includes(searchLower) ||
            candidate.candidate?.email?.toLowerCase().includes(searchLower) ||
            candidate.candidate?.id?.toLowerCase().includes(searchLower) ||
            candidate.resume?.Skills?.some(skill => {
                if (typeof skill === 'string') {
                    return skill.toLowerCase().includes(searchLower);
                }
                return skill.Skill?.toLowerCase().includes(searchLower);
            })
        );
    });

    if (!isOpen) return null;

    return (
        <>
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <motion.div
                    className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col overflow-hidden"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                >
                    {/* Header */}
                    <div className="bg-gradient-to-r from-[rgb(35,65,75)] to-gray-900 text-white p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h2 className="text-2xl font-bold">Candidates for {job?.title}</h2>
                                <p className="text-gray-300 mt-1">
                                    {filteredCandidates.length} candidate{filteredCandidates.length !== 1 ? 's' : ''} found
                                    {job?.applicants && ` (${job.applicants.length} applicants in job data)`}
                                </p>
                            </div>
                            <button
                                onClick={onClose}
                                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                            >
                                <XMarkIcon className="w-6 h-6" />
                            </button>
                        </div>

                        {/* Search and Filters */}
                        <div className="mt-4 flex gap-3">
                            <div className="flex-1 relative">
                                <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-white opacity-70" />
                                <input
                                    type="text"
                                    placeholder="Search candidates by name, email, or skills..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                    className="w-full pl-10 pr-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                                />
                            </div>
                            <button
                                onClick={() => setShowFilters(!showFilters)}
                                className="px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg hover:bg-opacity-30 transition-colors flex items-center gap-2"
                            >
                                <FunnelIcon className="w-5 h-5" />
                                Filters
                            </button>
                        </div>

                        {/* Filter Panel */}
                        <AnimatePresence>
                            {showFilters && (
                                <motion.div
                                    initial={{ height: 0, opacity: 0 }}
                                    animate={{ height: 'auto', opacity: 1 }}
                                    exit={{ height: 0, opacity: 0 }}
                                    className="mt-4 bg-white bg-opacity-10 rounded-lg p-4"
                                >
                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                        <select
                                            value={filters.status}
                                            onChange={(e) => handleFilterChange('status', e.target.value)}
                                            className="bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg px-3 py-2 text-white"
                                        >
                                            <option value="">All Status</option>
                                            <option value="applied">Applied</option>
                                            <option value="test_pending">Test Pending</option>
                                            <option value="test_completed">Test Completed</option>
                                            <option value="shortlisted">Shortlisted</option>
                                            <option value="rejected">Rejected</option>
                                        </select>

                                        <select
                                            value={filters.experienceLevel}
                                            onChange={(e) => handleFilterChange('experienceLevel', e.target.value)}
                                            className="bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg px-3 py-2 text-white"
                                        >
                                            <option value="">All Experience</option>
                                            <option value="entry">Entry Level</option>
                                            <option value="mid">Mid Level</option>
                                            <option value="senior">Senior Level</option>
                                        </select>

                                        <input
                                            type="text"
                                            placeholder="Skills"
                                            value={filters.skills}
                                            onChange={(e) => handleFilterChange('skills', e.target.value)}
                                            className="bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg px-3 py-2 text-white placeholder-white placeholder-opacity-70"
                                        />

                                        <input
                                            type="text"
                                            placeholder="Location"
                                            value={filters.location}
                                            onChange={(e) => handleFilterChange('location', e.target.value)}
                                            className="bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg px-3 py-2 text-white placeholder-white placeholder-opacity-70"
                                        />
                                    </div>

                                    <div className="flex gap-2 mt-4">
                                        <button
                                            onClick={applyFilters}
                                            className="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-opacity-90 transition-colors font-medium"
                                        >
                                            Apply Filters
                                        </button>
                                        <button
                                            onClick={clearFilters}
                                            className="px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg hover:bg-opacity-30 transition-colors"
                                        >
                                            Clear
                                        </button>
                                    </div>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>

                    {/* Content */}
                    <div className="flex-1 p-6 overflow-y-auto min-h-0">
                        {loading ? (
                            <div className="flex items-center justify-center py-12">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                            </div>
                        ) : filteredCandidates.length === 0 ? (
                            <div className="text-center py-12">
                                <UserIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No candidates found</h3>
                                <p className="text-gray-500">
                                    {searchTerm || Object.values(filters).some(f => f) 
                                        ? 'Try adjusting your search or filters' 
                                        : 'No candidates have applied for this job yet'}
                                </p>
                            </div>
                        ) : (
                            <div className="grid gap-4">
                                {filteredCandidates.map((application, index) => (
                                    <CandidateCard
                                        key={application.candidate?.id || index}
                                        application={application}
                                        onViewDetails={() => setSelectedCandidate(application)}
                                    />
                                ))}
                            </div>
                        )}
                    </div>
                </motion.div>
            </div>

            {/* Candidate Details Modal */}
            <CandidateDetailsModal
                isOpen={!!selectedCandidate}
                onClose={() => setSelectedCandidate(null)}
                candidateData={selectedCandidate}
                job={job}
            />
        </>
    );
};

// Candidate Card Component
const CandidateCard = ({ application, onViewDetails }) => {
    const { candidate, resume, application: appData, testResults } = application;

    return (
        <motion.div
            className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ y: -2 }}
        >
            <div className="flex items-start justify-between">
                <div className="flex items-start gap-4 flex-1">
                    {/* Avatar */}
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                        {candidate?.name?.charAt(0)?.toUpperCase() || candidate?.id?.charAt(0)?.toUpperCase() || 'C'}
                    </div>

                    {/* Candidate Info */}
                    <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                                {candidate?.name || `Candidate ${candidate?.id?.slice(-4) || 'Unknown'}`}
                            </h3>
                            {appData?.status && getStatusBadge(appData.status)}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-3">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <EnvelopeIcon className="w-4 h-4" />
                                {candidate?.email || 'No email provided'}
                            </div>

                            {resume?.Phone && (
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <PhoneIcon className="w-4 h-4" />
                                    {resume.Phone}
                                </div>
                            )}

                            {resume?.Location && (
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <MapPinIcon className="w-4 h-4" />
                                    {resume.Location}
                                </div>
                            )}

                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <BriefcaseIcon className="w-4 h-4" />
                                {candidate?.totalExperience || 0} years experience
                            </div>
                        </div>

                        {/* Skills */}
                        {resume?.Skills && resume.Skills.length > 0 ? (
                            <div className="flex flex-wrap gap-2 mb-3">
                                {resume.Skills.slice(0, 4).map((skill, index) => (
                                    <span
                                        key={index}
                                        className="px-2 py-1 bg-blue-100 text-blue-700 rounded-lg text-xs font-medium"
                                    >
                                        {skill.Skill || skill}
                                    </span>
                                ))}
                                {resume.Skills.length > 4 && (
                                    <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-lg text-xs font-medium">
                                        +{resume.Skills.length - 4} more
                                    </span>
                                )}
                            </div>
                        ) : (
                            <div className="mb-3">
                                <span className="px-2 py-1 bg-gray-100 text-gray-500 rounded-lg text-xs">
                                    No skills listed
                                </span>
                            </div>
                        )}

                        {/* Application Info */}
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span>Applied: {new Date(appData?.appliedAt).toLocaleDateString()}</span>
                            {testResults?.status && (
                                <span>Test: {testResults.status}</span>
                            )}
                        </div>
                    </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                    <button
                        onClick={onViewDetails}
                        className="flex items-center gap-1 px-3 py-2 bg-blue-50 text-blue-600 rounded-lg text-sm font-medium hover:bg-blue-100 transition-colors"
                    >
                        <EyeIcon className="w-4 h-4" />
                        View Details
                    </button>
                </div>
            </div>
        </motion.div>
    );
};

export default JobCandidatesModal;
