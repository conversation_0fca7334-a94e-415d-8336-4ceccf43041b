import React from 'react';
import { motion } from 'framer-motion';

const AddCompanyForm = ({ form, success, handleChange, handleSubmit }) => {
  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-gray-100 p-4">
      <div className="w-full max-w-lg">
        <motion.div
          className="relative bg-white rounded-2xl shadow-xl border border-gray-200 p-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <form className="flex flex-col gap-5" onSubmit={handleSubmit}>
            <h2 className="text-2xl font-bold text-center text-gray-800 mb-2">
              Company Details
            </h2>
            {[
              { label: 'Company Name', name: 'name', type: 'text', required: true },
              { label: 'Email', name: 'email', type: 'email', required: true },
              { label: 'Address', name: 'address', type: 'text', required: true },
              {
                label: 'Mode',
                name: 'mode',
                type: 'select',
                options: ['Online', 'Offline'],
                required: true,
              },
              {
                label: 'Timing',
                name: 'timing',
                type: 'text',
                required: true,
                placeholder: 'e.g. 9:00 AM - 5:00 PM',
              },
            ].map((field) => (
              <div key={field.name} className="flex flex-col gap-1">
                <label className="block font-semibold text-gray-700">
                  {field.label}
                </label>
                {field.type === 'select' ? (
                  <select
                    name={field.name}
                    value={form[field.name]}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none bg-gray-50 text-gray-800 transition"
                  >
                    {field.options.map((opt) => (
                      <option key={opt} value={opt}>
                        {opt}
                      </option>
                    ))}
                  </select>
                ) : (
                  <input
                    type={field.type}
                    name={field.name}
                    value={form[field.name]}
                    onChange={handleChange}
                    required={field.required}
                    placeholder={field.placeholder || ''}
                    className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none bg-gray-50 text-gray-800 transition"
                  />
                )}
              </div>
            ))}
            <button
              type="submit"
              className="mt-4 px-8 py-3 text-white rounded-xl font-bold shadow-lg transition-colors text-lg"
              style={{ background: 'rgb(35, 65, 75)' }}
            >
              Submit Company
            </button>
            {success && (
              <div className="text-green-600 font-semibold mt-2 text-center">
                Company added successfully!
              </div>
            )}
          </form>
        </motion.div>
      </div>
    </div>
  );
};

export default AddCompanyForm; 