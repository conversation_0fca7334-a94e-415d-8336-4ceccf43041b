import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';

const QuestionCard = ({ 
  question, 
  onEdit, 
  onDelete, 
  onView,
  showAnswer = false, 
  isSelected = false,
  onSelect,
  showActions = true,
  className = ""
}) => {
  const [showAnswerToggle, setShowAnswerToggle] = useState(showAnswer);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleEdit = (e) => {
    e.stopPropagation();
    onEdit?.(question);
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    onDelete?.(question._id || question.id);
  };

  const handleView = (e) => {
    e.stopPropagation();
    onView?.(question);
  };

  const handleSelect = () => {
    onSelect?.(question._id || question.id);
  };

  const toggleAnswer = (e) => {
    e.stopPropagation();
    setShowAnswerToggle(!showAnswerToggle);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const getOptionLetter = (index) => {
    return String.fromCharCode(65 + index); // A, B, C, D
  };

  const isCorrectOption = (option) => {
    return option && option.isCorrect === true;
  };

  const questionText = question.questionText || question.question;
  const category = question.category;

  return (
    <motion.div
      className={`bg-white rounded-xl shadow-lg border-2 transition-all duration-200 hover:shadow-xl ${
        isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300'
      } ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
      onClick={onSelect ? handleSelect : toggleExpanded}
      style={{ cursor: onSelect ? 'pointer' : 'default' }}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">
                {category || 'No Category'}
              </span>
              <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full">
                {question.questionType || 'MCQ'}
              </span>
              <span className="px-2 py-1 bg-purple-100 text-purple-600 text-xs rounded-full">
                {question.difficulty || 'Medium'}
              </span>
              {question.points && (
                <span className="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded-full">
                  {question.points} pts
                </span>
              )}
            </div>
            <h3 className={`font-semibold text-gray-800 leading-relaxed ${
              isExpanded ? 'text-base' : 'text-sm line-clamp-2'
            }`}>
              {questionText}
            </h3>
          </div>
          
          {showActions && (
            <div className="flex items-center gap-2 ml-4">
              {onView && (
                <button
                  onClick={handleView}
                  className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  title="View Details"
                >
                  <EyeIcon className="h-4 w-4" />
                </button>
              )}
              <button
                onClick={toggleAnswer}
                className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title={showAnswerToggle ? 'Hide Answer' : 'Show Answer'}
              >
                {showAnswerToggle ? (
                  <EyeSlashIcon className="h-4 w-4" />
                ) : (
                  <EyeIcon className="h-4 w-4" />
                )}
              </button>
              <button
                onClick={handleEdit}
                className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                title="Edit Question"
              >
                <PencilIcon className="h-4 w-4" />
              </button>
              <button
                onClick={handleDelete}
                className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Delete Question"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>

        {/* Options or Answer Display */}
        <div className="space-y-2">
          {question.options && Array.isArray(question.options) ? (
            // Display options for MCQ/Multiple-Select
            question.options.map((option, index) => {
              if (!option.text) return null;

              const isCorrect = isCorrectOption(option);

              return (
                <div
                  key={index}
                  className={`flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                    showAnswerToggle && isCorrect
                      ? 'bg-green-50 border-green-200 text-green-800'
                      : 'bg-gray-50 border-gray-200 text-gray-700'
                  }`}
                >
                  <div className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-semibold ${
                    showAnswerToggle && isCorrect
                      ? 'bg-green-200 text-green-800'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {getOptionLetter(index)}
                  </div>
                  <span className="flex-1 text-sm">{option.text}</span>
                  {showAnswerToggle && isCorrect && (
                    <CheckCircleIcon className="h-4 w-4 text-green-600" />
                  )}
                </div>
              );
            })
          ) : question.correctAnswer ? (
            // Display for Short-Answer/Code questions
            <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <span className="text-sm text-gray-600">Answer Type: </span>
              <span className="text-sm font-medium text-gray-800">
                {question.questionType === 'Code' ? 'Code Response' : 'Text Response'}
              </span>
              {showAnswerToggle && (
                <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm">
                  <strong>Answer:</strong> {question.correctAnswer}
                </div>
              )}
            </div>
          ) : (
            <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-500">
              No answer options available
            </div>
          )}
        </div>

        {/* Explanation Section */}
        <AnimatePresence>
          {showAnswerToggle && question.explanation && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 pt-4 border-t border-gray-200"
            >
              <div className="space-y-2">
                <span className="text-sm font-semibold text-gray-600">Explanation:</span>
                <p className="text-sm text-gray-700 bg-blue-50 p-3 rounded-lg border border-blue-200">
                  {question.explanation}
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Metadata */}
        {(question.createdAt || question.updatedAt) && (
          <div className="mt-4 pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between text-xs text-gray-500">
              {question.createdAt && (
                <span>Created: {new Date(question.createdAt).toLocaleDateString()}</span>
              )}
              {question.updatedAt && (
                <span>Updated: {new Date(question.updatedAt).toLocaleDateString()}</span>
              )}
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default QuestionCard;
