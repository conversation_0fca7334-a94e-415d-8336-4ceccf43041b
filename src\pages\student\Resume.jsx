import React, { useEffect } from "react";
import useAuthStore from "../../store/authStore";
import useStudentStore from "../../store/studentStore";

const ImageInspiredTemplate = () => {
  const { user, fetchCurrentUser } = useAuthStore();
  const { profile, fetchProfile, profileLoading } = useStudentStore();

  useEffect(() => {
    if (!user) fetchCurrentUser();
  }, []);

  useEffect(() => {
    if (user && !profile._id) fetchProfile();
  }, [user]);

  if (profileLoading || !profile._id) {
    return (
      <div className="text-center py-20 text-gray-400 text-lg">
        Loading resume...
      </div>
    );
  }

  // Extract data from the student profile (API response structure)
  const resumeData = profile.apiData?.profile || {};

  const {
    Title = profile.name || user?.name || "",
    Email = profile.email || user?.email || "",
    Headline = profile.headline || "",
    Phone = resumeData.Phone || "",
    Location = resumeData.Location || profile.address || "",
    Website = resumeData.Website || "",
    ProfilePic = profile.avatar || resumeData.ProfilePic || "",
    Profiles = resumeData.Profiles || [],
    Experience = resumeData.Experience || [],
    Education = resumeData.Education || [],
    Skills = resumeData.Skills || [],
    Languages = resumeData.Languages || [],
    Certifications = resumeData.Certifications || [],
    Awards = resumeData.Awards || [],
    Projects = resumeData.Projects || [],
    Publications = resumeData.Publications || [],
    Volunteering = resumeData.Volunteering || [],
    References = resumeData.References || [],
    Interests = resumeData.Interests || [],
    summery = profile.about || resumeData.summery || "",
  } = resumeData;

  return (
    <div className="max-w-6xl mx-auto p-8 bg-gradient-to-br from-white to-blue-50 rounded-3xl shadow-xl font-sans text-gray-800">
      {/* Header */}
      <div className="flex flex-col md:flex-row items-center gap-6 border-b pb-6 mb-8">
        {ProfilePic && (
          <img
            src={ProfilePic}
            alt="Profile"
            className="w-28 h-28 rounded-xl object-cover shadow-md"
          />
        )}
        <div className="text-center md:text-left">
          <h1 className="text-4xl font-bold text-blue-900">{Title}</h1>
          {Headline && <p className="text-lg text-blue-700">{Headline}</p>}
          <div className="mt-2 space-y-1 text-sm text-gray-600">
            {Email && (
              <p>
                ✉️{" "}
                <a href={`mailto:${Email}`} className="underline text-blue-700">
                  {Email}
                </a>
              </p>
            )}
            {Phone && <p>📞 {Phone}</p>}
            {Location && <p>📍 {Location}</p>}
            {Website && (
              <p>
                🌐{" "}
                <a
                  href={Website}
                  target="_blank"
                  rel="noreferrer"
                  className="underline text-blue-700"
                >
                  {Website}
                </a>
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Section Generator */}
      {summery && <Section title="Summary" content={<p>{summery}</p>} />}

      {Profiles.length > 0 && (
        <Section
          title="Profiles"
          content={Profiles.map((p) => (
            <p key={p._id}>
              <strong>{p.Network}:</strong>{" "}
              <a
                href={p.ProfileLink}
                target="_blank"
                rel="noreferrer"
                className="text-blue-600 underline"
              >
                {p.Username}
              </a>
            </p>
          ))}
        />
      )}

      {Experience.length > 0 && (
        <Section
          title="Experience"
          content={Experience.map((exp) => (
            <div key={exp._id} className="mb-4">
              <h3 className="text-lg font-semibold">
                {exp.Position} at {exp.Company}
              </h3>
              <p className="italic text-sm text-gray-500">{exp.Location}</p>
              <p className="text-sm">
                {formatDate(exp.StartDate)} - {formatDate(exp.EndDate)}
              </p>
              {exp.Description && (
                <p className="text-sm mt-1">{exp.Description}</p>
              )}
              {exp.Website && (
                <a
                  href={exp.Website}
                  target="_blank"
                  rel="noreferrer"
                  className="text-blue-600 text-sm underline"
                >
                  {exp.Website}
                </a>
              )}
            </div>
          ))}
        />
      )}

      {Projects.length > 0 && (
        <Section
          title="Projects"
          content={Projects.map((proj) => (
            <div key={proj._id} className="mb-4">
              <h3 className="font-semibold">{proj.Title}</h3>
              <p className="text-sm">{proj.Description}</p>
              {proj.Technologies?.length > 0 && (
                <p className="text-xs text-gray-600">
                  Technologies: {proj.Technologies.join(", ")}
                </p>
              )}
              {proj.Link && (
                <a
                  href={proj.Link}
                  target="_blank"
                  rel="noreferrer"
                  className="text-blue-600 text-sm underline"
                >
                  {proj.Link}
                </a>
              )}
              <p className="text-xs text-gray-500">
                {formatDate(proj.StartDate)} - {formatDate(proj.EndDate)}
              </p>
            </div>
          ))}
        />
      )}

      {Education.length > 0 && (
        <Section
          title="Education"
          content={Education.map((edu) => (
            <div key={edu._id}>
              <p className="font-medium">
                {edu.Degree} at {edu.Institution}
              </p>
              <p className="text-sm text-gray-600">
                {formatDate(edu.StartDate)} - {formatDate(edu.EndDate)}
              </p>
              <p className="text-sm italic">{edu.Location}</p>
            </div>
          ))}
        />
      )}

      {Skills.length > 0 && (
        <Section
          title="Skills"
          content={Skills.map((skill) => (
            <div key={skill._id}>
              <p className="font-medium">
                {skill.Skill} ({skill.Proficiency})
              </p>
              <p className="text-sm">{skill.Description}</p>
              {skill.Keywords?.length > 0 && (
                <p className="text-xs text-gray-600">
                  Keywords: {skill.Keywords.join(", ")}
                </p>
              )}
            </div>
          ))}
        />
      )}

      {Languages.length > 0 && (
        <Section
          title="Languages"
          content={Languages.map((lang) => (
            <p key={lang._id}>
              {lang.Name} - {lang.Proficiency}{" "}
              {lang.Description && `(${lang.Description})`}
            </p>
          ))}
        />
      )}

      {Certifications.length > 0 && (
        <Section
          title="Certifications"
          content={Certifications.map((cert) => (
            <div key={cert._id}>
              <p>
                {cert.Title} by {cert.Issuer}
              </p>
              <p className="text-sm text-gray-600">{formatDate(cert.Date)}</p>
              {cert.Description && (
                <p className="text-sm">{cert.Description}</p>
              )}
              {cert.Website && (
                <a
                  href={cert.Website}
                  target="_blank"
                  rel="noreferrer"
                  className="text-blue-600 underline"
                >
                  {cert.Website}
                </a>
              )}
            </div>
          ))}
        />
      )}

      {Awards.length > 0 && (
        <Section
          title="Awards"
          content={Awards.map((award) => (
            <div key={award._id}>
              <p className="font-medium">{award.Title} by {award.Issuer}</p>
              <p className="text-sm text-gray-600">{formatDate(award.Date)}</p>
              {award.Description && (
                <p className="text-sm">{award.Description}</p>
              )}
            </div>
          ))}
        />
      )}

      {Publications.length > 0 && (
        <Section
          title="Publications"
          content={Publications.map((pub) => (
            <div key={pub._id}>
              <p className="font-medium">{pub.Title}</p>
              <p className="text-sm">{pub.Publisher}</p>
              <p className="text-xs text-gray-500">{formatDate(pub.Date)}</p>
              {pub.Website && (
                <a
                  href={pub.Website}
                  className="text-blue-600 underline text-sm"
                  target="_blank"
                  rel="noreferrer"
                >
                  {pub.Website}
                </a>
              )}
              {pub.Description && (
                <p className="text-sm mt-1">{pub.Description}</p>
              )}
            </div>
          ))}
        />
      )}

      {Volunteering.length > 0 && (
        <Section
          title="Volunteering"
          content={Volunteering.map((vol) => (
            <div key={vol._id}>
              <p className="font-medium">
                {vol.Position} at {vol.Organization}
              </p>
              <p className="text-sm">{vol.Location}</p>
              <p className="text-sm text-gray-600">
                {formatDate(vol.StartDate)} - {formatDate(vol.EndDate)}
              </p>
              {vol.Description && (
                <p className="text-sm mt-1">{vol.Description}</p>
              )}
            </div>
          ))}
        />
      )}

      {References.length > 0 && (
        <Section
          title="References"
          content={References.map((ref) => (
            <div key={ref._id}>
              <p className="font-medium">{ref.Name}</p>
              <p className="text-sm">
                {ref.Position} at {ref.Company}
              </p>
              <p className="text-sm">
                {ref.Email} | {ref.Phone}
              </p>
            </div>
          ))}
        />
      )}

      {Interests.length > 0 && (
        <Section
          title="Interests"
          content={
            <ul className="list-disc pl-5">
              {Interests.map((interest, idx) => (
                <li key={idx}>{interest.Interest || interest}</li>
              ))}
            </ul>
          }
        />
      )}
    </div>
  );
};

const Section = ({ title, content }) => (
  <div className="mb-8">
    <h2 className="text-2xl font-semibold border-b-2 border-blue-300 mb-3">
      {title}
    </h2>
    <div className="space-y-2 text-sm text-gray-800">{content}</div>
  </div>
);

const formatDate = (date) => {
  if (!date) return 'Present';
  return new Date(date).toLocaleDateString();
};

export default function Resume() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-[#23414c] mb-2">My Resume</h1>
          <p className="text-gray-600 mb-4">Professional resume generated from your profile data</p>

          {/* Create More Resumes Button */}
          <div className="mb-6">
            <a
              href="https://resumebuilder.proyuj.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              Create More Professional Resumes
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                />
              </svg>
            </a>
            <p className="text-sm text-gray-500 mt-2">
              Build custom resumes with different templates and designs
            </p>
          </div>
        </div>
        <ImageInspiredTemplate />
      </div>
    </div>
  );
}