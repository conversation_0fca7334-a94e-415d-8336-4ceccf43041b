import { create } from 'zustand';
import axios from 'axios';
import { ADMIN_ENDPOINTS } from '../lib/constants';

// Create axios instance with interceptor
const axiosInstance = axios.create({
    withCredentials: true,
});

// Add request interceptor to include auth token
axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

const useAdminStore = create((set, get) => ({
    // State
    users: [],
    companies: [],
    pendingCompanies: [],
    jobPosts: [],
    tests: [],
    settings: null,
    loading: false,
    error: null,

    // Pagination and filtering state
    usersPagination: { current: 1, pages: 1, total: 0 },
    companiesPagination: { current: 1, pages: 1, total: 0 },
    jobsPagination: { current: 1, pages: 1, total: 0 },
    testsPagination: { current: 1, pages: 1, total: 0 },

    // Statistics
    statistics: {
        totalUsers: 0,
        totalCompanies: 0,
        totalJobs: 0,
        totalTests: 0,
        activeUsers: 0,
        activeCompanies: 0,
        pendingCompanies: 0,
        flaggedJobs: 0,
        flaggedTests: 0
    },

    // Cache and request management
    cache: {
        users: { data: null, timestamp: null, params: null },
        companies: { data: null, timestamp: null, params: null },
        jobs: { data: null, timestamp: null, params: null },
        tests: { data: null, timestamp: null, params: null },
        statistics: { data: null, timestamp: null }
    },
    pendingRequests: new Map(),
    cacheTimeout: 5 * 60 * 1000, // 5 minutes cache timeout

    // Loading states for different operations
    loadingStates: {
        users: false,
        companies: false,
        jobs: false,
        tests: false,
        statistics: false,
        refreshing: false
    },

    // Basic setters
    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),
    clearError: () => set({ error: null }),

    // Loading state management
    setLoadingState: (key, loading) => {
        set((state) => ({
            loadingStates: {
                ...state.loadingStates,
                [key]: loading
            },
            // Also update global loading if any operation is loading
            loading: loading || Object.values({
                ...state.loadingStates,
                [key]: loading
            }).some(Boolean)
        }));
    },

    isLoading: (key) => {
        const state = get();
        return key ? state.loadingStates[key] : state.loading;
    },

    // Enhanced error handler
    handleError: (err, defaultMessage) => {
        const errorMessage = err?.response?.data?.error ||
            err?.response?.data?.message ||
            err?.message ||
            defaultMessage;
        set({ error: errorMessage });
        console.error('Admin Store Error:', err);
        return { success: false, error: errorMessage };
    },

    // Cache management helpers
    isCacheValid: (cacheKey, params = null) => {
        const state = get();
        const cacheEntry = state.cache[cacheKey];

        if (!cacheEntry || !cacheEntry.timestamp) return false;

        const now = Date.now();
        const isExpired = (now - cacheEntry.timestamp) > state.cacheTimeout;

        // Check if params have changed (for parameterized requests)
        const paramsChanged = params && JSON.stringify(params) !== JSON.stringify(cacheEntry.params);

        return !isExpired && !paramsChanged;
    },

    updateCache: (cacheKey, data, params = null) => {
        set((state) => ({
            cache: {
                ...state.cache,
                [cacheKey]: {
                    data,
                    timestamp: Date.now(),
                    params: params ? JSON.parse(JSON.stringify(params)) : null
                }
            }
        }));
    },

    clearCache: (cacheKey = null) => {
        if (cacheKey) {
            set((state) => ({
                cache: {
                    ...state.cache,
                    [cacheKey]: { data: null, timestamp: null, params: null }
                }
            }));
        } else {
            set({
                cache: {
                    users: { data: null, timestamp: null, params: null },
                    companies: { data: null, timestamp: null, params: null },
                    jobs: { data: null, timestamp: null, params: null },
                    tests: { data: null, timestamp: null, params: null },
                    statistics: { data: null, timestamp: null }
                }
            });
        }
    },

    // Request deduplication helper
    //TODO: CHECK WHERE WORK
    createRequestKey: (endpoint, params = {}) => {
        return `${endpoint}_${JSON.stringify(params)}`;
    },
    //TODO :cHECK IT WHERE WORK HOW IT WORK
    executeWithDeduplication: async (requestKey, requestFn) => {
        const state = get();

        // Check if request is already pending
        if (state.pendingRequests.has(requestKey)) {
            return await state.pendingRequests.get(requestKey);
        }

        // Create and store the promise
        const requestPromise = requestFn();
        state.pendingRequests.set(requestKey, requestPromise);

        try {
            const result = await requestPromise;
            return result;
        } finally {
            // Clean up the pending request
            state.pendingRequests.delete(requestKey);
        }
    },

    // USERS MANAGEMENT
    getUsers: async (params = {}) => {
        const state = get();
        const requestKey = state.createRequestKey('users', params);

        // Prevent multiple simultaneous requests
        if (state.isLoading('users')) {
            console.log('Users request already in progress, skipping...');
            return { success: false, message: 'Request already in progress' };
        }

        // Check cache first (only for non-search requests to avoid stale search results)
        if (!params.search && state.isCacheValid('users', params)) {
            const cachedData = state.cache.users.data;
            if (cachedData) {
                set({
                    users: cachedData.users || [],
                    usersPagination: cachedData.pagination || { current: 1, pages: 1, total: 0 }
                });
                return { success: true, data: cachedData, fromCache: true };
            }
        }

        return await state.executeWithDeduplication(requestKey, async () => {
            state.setLoadingState('users', true);
            set({ error: null });
            try {
                const queryParams = new URLSearchParams();
                // Add pagination parameters
                if (params.page) queryParams.append('page', params.page);
                if (params.limit) queryParams.append('limit', params.limit);
                // Add filter parameters
                if (params.search) queryParams.append('search', params.search);
                if (params.status) queryParams.append('status', params.status);
                if (params.role) queryParams.append('role', params.role);
                if (params.sortBy) queryParams.append('sortBy', params.sortBy);
                if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
                const url = `${ADMIN_ENDPOINTS.USERS}${queryParams.toString() ? `?${queryParams}` : ''}`;
                const res = await axiosInstance.get(url);
                const responseData = {
                    users: res.data.data || res.data.users || [],
                    pagination: {
                        current: res.data.pagination?.currentPage || 1,
                        pages: res.data.pagination?.totalPages || 1,
                        total: res.data.pagination?.total || 0
                    }
                };

                set({
                    users: responseData.users,
                    usersPagination: responseData.pagination
                });

                // Cache the response (only for non-search requests)
                if (!params.search) {
                    state.updateCache('users', responseData, params);
                }

                return { success: true, data: res.data };
            } catch (err) {
                return get().handleError(err, 'Fetch users failed');
            } finally {
                state.setLoadingState('users', false);
            }
        });
    },

    getUserById: async (userId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.USER(userId));
            return {
                success: true,
                user: res.data.user,
                profile: res.data.profile // Include profile data if available
            };
        } catch (err) {
            return get().handleError(err, 'Fetch user failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add activate user CHECK WHY NOT WORKING
    activateUser: async (userId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(ADMIN_ENDPOINTS.ACTIVATE_USER(userId));

            // Update local state
            set((state) => ({
                users: state.users.map(user =>
                    user._id === userId ? { ...user, isVerified: true, isActive: true } : user
                ),
                statistics: {
                    ...state.statistics,
                    activeUsers: state.statistics.activeUsers + 1
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Activate user failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add deactivate user CHECK WHY NOT WORKING
    deactivateUser: async (userId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.USER(userId)}/deactivate`);

            // Update local state
            set((state) => ({
                users: state.users.map(user =>
                    user._id === userId ? { ...user, isVerified: false, isActive: false } : user
                ),
                statistics: {
                    ...state.statistics,
                    activeUsers: Math.max(0, state.statistics.activeUsers - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Deactivate user failed');
        } finally {
            set({ loading: false });
        }
    },

    updateUserRole: async (userId, role) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.patch(ADMIN_ENDPOINTS.USER(userId), { role });

            // Update local state
            set((state) => ({
                users: state.users.map(user =>
                    user._id === userId ? { ...user, role } : user
                )
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Update user role failed');
        } finally {
            set({ loading: false });
        }
    },

    bulkUpdateUsers: async (userIds, updateData) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.patch(`${ADMIN_ENDPOINTS.USERS}/bulk`, {
                userIds,
                updateData
            });

            // Update local state
            set((state) => ({
                users: state.users.map(user =>
                    userIds.includes(user._id) ? { ...user, ...updateData } : user
                )
            }));

            return { success: true, message: res.data.message, updated: res.data.updated };
        } catch (err) {
            return get().handleError(err, 'Bulk update users failed');
        } finally {
            set({ loading: false });
        }
    },

    // COMPANIES MANAGEMENT - Filter from users instead of separate API
    getCompanies: async (params = {}) => {
        set({ loading: true, error: null });
        try {
            // Get users and filter for company role
            const usersResult = await get().getUsers({ role: 'company', ...params });

            if (usersResult.success) {
                // Transform user data to company format
                const companies = get().users
                    .filter(user => user.role === 'company')
                    .map(user => ({
                        _id: user._id,
                        companyName: user.companyName || user.name || 'Unknown Company',
                        email: user.email,
                        isVerified: user.isVerified,
                        isActive: user.isActive,
                        createdAt: user.createdAt,
                        updatedAt: user.updatedAt,
                        // Additional company fields if available
                        industry: user.industry,
                        companySize: user.companySize,
                        website: user.website,
                        description: user.description,
                        location: user.location,
                        // Map user fields to company fields
                        name: user.companyName || user.name,
                        status: user.isActive ? 'active' : 'inactive'
                    }));

                set({
                    companies,
                    companiesPagination: get().usersPagination // Use same pagination as users
                });

                return { success: true, data: { companies, pagination: get().usersPagination } };
            }

            return usersResult;
        } catch (err) {
            return get().handleError(err, 'Fetch companies failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add pending companies in both backend and frontend
    getPendingCompanies: async (params = {}) => {
        set({ loading: true, error: null });
        try {
            // Get pending companies from users with company role and not verified
            const usersResult = await get().getUsers({ role: 'company', isVerified: false, ...params });

            if (usersResult.success) {
                const pendingCompanies = get().users
                    .filter(user => user.role === 'company' && !user.isVerified)
                    .map(user => ({
                        _id: user._id,
                        companyName: user.companyName || user.name || 'Unknown Company',
                        email: user.email,
                        isVerified: user.isVerified,
                        isActive: user.isActive,
                        createdAt: user.createdAt,
                        updatedAt: user.updatedAt,
                        industry: user.industry,
                        companySize: user.companySize,
                        website: user.website,
                        description: user.description,
                        location: user.location,
                        name: user.companyName || user.name,
                        status: 'pending'
                    }));

                set({ pendingCompanies });
                return { success: true, data: { companies: pendingCompanies } };
            }

            return usersResult;
        } catch (err) {
            return get().handleError(err, 'Fetch pending companies failed');
        } finally {
            set({ loading: false });
        }
    },

    getCompanyDetails: async (companyId) => {
        set({ loading: true, error: null });
        try {
            // Get company details from user data
            const userResult = await get().getUserById(companyId);

            if (userResult.success && userResult.user.role === 'company') {
                const company = {
                    ...userResult.user,
                    companyName: userResult.user.companyName || userResult.user.name,
                    name: userResult.user.companyName || userResult.user.name,
                    status: userResult.user.isActive ? 'active' : 'inactive'
                };

                return { success: true, company };
            }

            return { success: false, error: 'Company not found' };
        } catch (err) {
            return get().handleError(err, 'Fetch company details failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add approve company in both backend and frontend
    approveCompany: async (companyId, approvalData = {}) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(ADMIN_ENDPOINTS.COMPANY(companyId), approvalData);

            // Update local state
            set((state) => ({
                pendingCompanies: state.pendingCompanies.filter(company => company._id !== companyId),
                companies: [...state.companies, res.data.company],
                statistics: {
                    ...state.statistics,
                    totalCompanies: state.statistics.totalCompanies + 1,
                    activeCompanies: state.statistics.activeCompanies + 1,
                    pendingCompanies: Math.max(0, state.statistics.pendingCompanies - 1)
                }
            }));

            return { success: true, message: res.data.message, company: res.data.company };
        } catch (err) {
            return get().handleError(err, 'Approve company failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add reject company in both backend and frontend
    rejectCompany: async (companyId, rejectionReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.COMPANY(companyId)}/reject`, {
                reason: rejectionReason
            });

            // Update local state
            set((state) => ({
                pendingCompanies: state.pendingCompanies.filter(company => company._id !== companyId),
                statistics: {
                    ...state.statistics,
                    pendingCompanies: Math.max(0, state.statistics.pendingCompanies - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Reject company failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add suspend company in both backend and frontend
    suspendCompany: async (companyId, suspensionReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.COMPANY(companyId)}/suspend`, {
                reason: suspensionReason
            });

            // Update local state
            set((state) => ({
                companies: state.companies.map(company =>
                    company._id === companyId
                        ? { ...company, status: 'suspended', suspensionReason }
                        : company
                ),
                statistics: {
                    ...state.statistics,
                    activeCompanies: Math.max(0, state.statistics.activeCompanies - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Suspend company failed');
        } finally {
            set({ loading: false });
        }
    },

    getCompanyDetails: async (companyId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.COMPANY(companyId));
            return { success: true, company: res.data.company };
        } catch (err) {
            return get().handleError(err, 'Fetch company details failed');
        } finally {
            set({ loading: false });
        }
    },

    // JOBS MANAGEMENT
    getJobs: async (params = {}) => {
        const state = get();
        const requestKey = state.createRequestKey('jobs', params);

        // Prevent multiple simultaneous requests
        if (state.isLoading('jobs')) {
            console.log('Jobs request already in progress, skipping...');
            return { success: false, message: 'Request already in progress' };
        }

        // Check cache first (only for non-search requests)
        if (!params.search && state.isCacheValid('jobs', params)) {
            const cachedData = state.cache.jobs.data;
            if (cachedData) {
                set({
                    jobPosts: cachedData.jobs || [],
                    jobsPagination: cachedData.pagination || { current: 1, pages: 1, total: 0 }
                });
                return { success: true, data: cachedData, fromCache: true };
            }
        }

        return await state.executeWithDeduplication(requestKey, async () => {
            state.setLoadingState('jobs', true);
            set({ error: null });
            try {
                const queryParams = new URLSearchParams();

                Object.entries(params).forEach(([key, value]) => {
                    if (value !== undefined && value !== null && value !== '') {
                        queryParams.append(key, value);
                    }
                });

                const url = `${ADMIN_ENDPOINTS.JOBS}${queryParams.toString() ? `?${queryParams}` : ''}`;
                const res = await axiosInstance.get(url);

                // Transform the job data to match component expectations
                const transformedJobs = (res.data.data || res.data.jobs || []).map(job => ({
                    ...job,
                    // Normalize company data
                    company: job.companyId ? {
                        name: job.companyId.companyName || job.companyId.name,
                        _id: job.companyId._id
                    } : null,
                    companyName: job.companyId?.companyName || job.companyName,
                    // Normalize applications count
                    applicationsCount: job.currentApplications || job.applicationsCount || 0,
                    // Normalize status
                    status: job.isActive ? 'active' : (job.status || 'inactive'),
                    // Normalize salary display
                    salary: job.salary ?
                        `${job.salary.currency || 'INR'} ${job.salary.min || 0} - ${job.salary.max || 0}` :
                        (job.salaryRange || 'Not specified')
                }));

                const responseData = {
                    jobs: transformedJobs,
                    pagination: res.data.pagination || { current: 1, pages: 1, total: 0 }
                };

                set({
                    jobPosts: responseData.jobs,
                    jobsPagination: responseData.pagination
                });

                // Cache the response (only for non-search requests)
                if (!params.search) {
                    state.updateCache('jobs', responseData, params);
                }

                return { success: true, data: res.data };
            } catch (err) {
                return get().handleError(err, 'Fetch jobs failed');
            } finally {
                state.setLoadingState('jobs', false);
            }
        });
    },

    getJobById: async (jobId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.JOB(jobId));

            // Transform the job data to match component expectations
            const job = res.data.job || res.data.data;
            const transformedJob = job ? {
                ...job,
                // Normalize company data
                company: job.companyId ? {
                    name: job.companyId.companyName || job.companyId.name,
                    _id: job.companyId._id
                } : null,
                companyName: job.companyId?.companyName || job.companyName,
                // Normalize applications count
                applicationsCount: job.currentApplications || job.applicationsCount || 0,
                // Normalize status
                status: job.isActive ? 'active' : (job.status || 'inactive'),
                // Normalize salary display
                salary: job.salary ?
                    `${job.salary.currency || 'INR'} ${job.salary.min || 0} - ${job.salary.max || 0}` :
                    (job.salaryRange || 'Not specified')
            } : null;

            return { success: true, job: transformedJob };
        } catch (err) {
            return get().handleError(err, 'Fetch job failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add delete job and flag job
    flagJob: async (jobId, flagReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(ADMIN_ENDPOINTS.FLAG_JOB(jobId), {
                reason: flagReason
            });

            // Update local state
            set((state) => ({
                jobPosts: state.jobPosts.map(job =>
                    job._id === jobId
                        ? { ...job, isFlagged: true, flagReason, status: 'flagged' }
                        : job
                ),
                statistics: {
                    ...state.statistics,
                    flaggedJobs: state.statistics.flaggedJobs + 1
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Flag job failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add unflag job
    unflagJob: async (jobId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.JOB(jobId)}/unflag`);

            // Update local state
            set((state) => ({
                jobPosts: state.jobPosts.map(job =>
                    job._id === jobId
                        ? { ...job, isFlagged: false, flagReason: null, status: 'active' }
                        : job
                ),
                statistics: {
                    ...state.statistics,
                    flaggedJobs: Math.max(0, state.statistics.flaggedJobs - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Unflag job failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add bulk flag jobs
    bulkFlagJobs: async (jobIds, flagReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.JOBS}/bulk-flag`, {
                jobIds,
                reason: flagReason
            });

            // Update local state
            set((state) => ({
                jobPosts: state.jobPosts.map(job =>
                    jobIds.includes(job._id)
                        ? { ...job, isFlagged: true, flagReason, status: 'flagged' }
                        : job
                ),
                statistics: {
                    ...state.statistics,
                    flaggedJobs: state.statistics.flaggedJobs + jobIds.length
                }
            }));

            return { success: true, message: res.data.message, flagged: jobIds.length };
        } catch (err) {
            return get().handleError(err, 'Bulk flag jobs failed');
        } finally {
            set({ loading: false });
        }
    },

    // TESTS MANAGEMENT
    //TODO: Tis is not implemented yet in frontend
    getTests: async (params = {}) => {
        const state = get();
        const requestKey = state.createRequestKey('tests', params);

        // Prevent multiple simultaneous requests
        if (state.isLoading('tests')) {
            console.log('Tests request already in progress, skipping...');
            return { success: false, message: 'Request already in progress' };
        }

        // Check cache first (only for non-search requests)
        if (!params.search && state.isCacheValid('tests', params)) {
            const cachedData = state.cache.tests.data;
            if (cachedData) {
                set({
                    tests: cachedData.tests || [],
                    testsPagination: cachedData.pagination || { current: 1, pages: 1, total: 0 }
                });
                return { success: true, data: cachedData, fromCache: true };
            }
        }

        return await state.executeWithDeduplication(requestKey, async () => {
            state.setLoadingState('tests', true);
            set({ error: null });
            try {
                const queryParams = new URLSearchParams();

                Object.entries(params).forEach(([key, value]) => {
                    if (value !== undefined && value !== null && value !== '') {
                        queryParams.append(key, value);
                    }
                });

                const url = `${ADMIN_ENDPOINTS.TESTS}${queryParams.toString() ? `?${queryParams}` : ''}`;
                const res = await axiosInstance.get(url);

                const responseData = {
                    tests: res.data.tests || [],
                    pagination: res.data.pagination || { current: 1, pages: 1, total: 0 }
                };

                set({
                    tests: responseData.tests,
                    testsPagination: responseData.pagination
                });

                // Cache the response (only for non-search requests)
                if (!params.search) {
                    state.updateCache('tests', responseData, params);
                }

                return { success: true, data: res.data };
            } catch (err) {
                return get().handleError(err, 'Fetch tests failed');
            } finally {
                state.setLoadingState('tests', false);
            }
        });
    },
    //TODO: Not implemented yet in frontend
    getTestById: async (testId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.TEST(testId));
            return { success: true, test: res.data.test };
        } catch (err) {
            return get().handleError(err, 'Fetch test failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add flag test not implemented yet in frontend
    flagTest: async (testId, flagReason) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(ADMIN_ENDPOINTS.FLAG_TEST(testId), {
                reason: flagReason
            });

            // Update local state
            set((state) => ({
                tests: state.tests.map(test =>
                    test._id === testId
                        ? { ...test, isFlagged: true, flagReason, status: 'flagged' }
                        : test
                ),
                statistics: {
                    ...state.statistics,
                    flaggedTests: state.statistics.flaggedTests + 1
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Flag test failed');
        } finally {
            set({ loading: false });
        }
    },

    unflagTest: async (testId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.TEST(testId)}/unflag`);

            // Update local state
            set((state) => ({
                tests: state.tests.map(test =>
                    test._id === testId
                        ? { ...test, isFlagged: false, flagReason: null, status: 'active' }
                        : test
                ),
                statistics: {
                    ...state.statistics,
                    flaggedTests: Math.max(0, state.statistics.flaggedTests - 1)
                }
            }));

            return { success: true, message: res.data.message };
        } catch (err) {
            return get().handleError(err, 'Unflag test failed');
        } finally {
            set({ loading: false });
        }
    },

    getTestAnalytics: async (testId) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.TEST(testId)}/analytics`);
            return { success: true, analytics: res.data.analytics };
        } catch (err) {
            return get().handleError(err, 'Fetch test analytics failed');
        } finally {
            set({ loading: false });
        }
    },

    // SETTINGS MANAGEMENT
    getSettings: async () => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(ADMIN_ENDPOINTS.SETTINGS);
            set({ settings: res.data.settings });
            return { success: true, settings: res.data.settings };
        } catch (err) {
            return get().handleError(err, 'Fetch settings failed');
        } finally {
            set({ loading: false });
        }
    },

    updateSettings: async (settingsData) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.put(ADMIN_ENDPOINTS.SETTINGS, settingsData);
            set({ settings: res.data.settings });
            return { success: true, message: res.data.message, settings: res.data.settings };
        } catch (err) {
            return get().handleError(err, 'Update settings failed');
        } finally {
            set({ loading: false });
        }
    },

    // DASHBOARD & ANALYTICS - Calculate stats from existing data with caching
    //TODO: Add loading state for dashboard stats   
    getDashboardStats: async (forceRefresh = false) => {
        const state = get();
        // Check cache first
        if (!forceRefresh && state.isCacheValid('statistics')) {
            const cachedStats = state.cache.statistics.data;
            if (cachedStats) {
                set({ statistics: cachedStats });
                return { success: true, statistics: cachedStats, fromCache: true };
            }
        }
        return await state.executeWithDeduplication('dashboard_stats', async () => {
            state.setLoadingState('statistics', true);
            set({ error: null });
            try {
                // Only fetch data if we don't have recent cached data
                const promises = [];
                if (!state.isCacheValid('users')) {
                    promises.push(get().getUsers({ limit: 1000 }));
                }
                if (!state.isCacheValid('jobs')) {
                    promises.push(get().getJobs({ limit: 1000 }));
                }
                if (!state.isCacheValid('tests')) {
                    promises.push(get().getTests({ limit: 1000 }));
                }
                // Wait for any pending data fetches
                if (promises.length > 0) {
                    await Promise.allSettled(promises);
                }
                const { users, jobPosts, tests } = get();
                // Calculate statistics from the loaded data
                const statistics = {
                    totalUsers: users?.length || 0,
                    totalCompanies: users?.filter(user => user.role === 'company')?.length || 0,
                    totalJobs: jobPosts?.length || 0,
                    totalTests: tests?.length || 0,
                    activeUsers: users?.filter(user => user.isActive)?.length || 0,
                    activeCompanies: users?.filter(user => user.role === 'company' && user.isActive)?.length || 0,
                    pendingCompanies: users?.filter(user => user.role === 'company' && !user.isVerified)?.length || 0,
                    flaggedJobs: jobPosts?.filter(job => job.flaggedBy || job.isFlagged)?.length || 0,
                    flaggedTests: tests?.filter(test => test.flaggedBy || test.isFlagged)?.length || 0
                };
                set({ statistics });
                // Cache the statistics
                state.updateCache('statistics', statistics);
                return { success: true, statistics };
            } catch (err) {
                // If there's an error, return default statistics
                const defaultStats = {
                    totalUsers: 0,
                    totalCompanies: 0,
                    totalJobs: 0,
                    totalTests: 0,
                    activeUsers: 0,
                    activeCompanies: 0,
                    pendingCompanies: 0,
                    flaggedJobs: 0,
                    flaggedTests: 0
                };
                set({ statistics: defaultStats });
                return { success: true, statistics: defaultStats };
            } finally {
                state.setLoadingState('statistics', false);
            }
        });
    },
    //TODO: Add analytics for users, companies, jobs, and tests
    getSystemAnalytics: async (period = 'week') => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.SETTINGS}/analytics?period=${period}`);
            return { success: true, analytics: res.data.analytics };
        } catch (err) {
            return get().handleError(err, 'Fetch system analytics failed');
        } finally {
            set({ loading: false });
        }
    },

    // SEARCH & FILTER UTILITIES
    //TODO: Add search for users
    searchUsers: async (searchTerm, filters = {}) => {
        const params = { search: searchTerm, ...filters };
        return await get().getUsers(params);
    },

    searchCompanies: async (searchTerm, filters = {}) => {
        const params = { search: searchTerm, ...filters };
        return await get().getCompanies(params);
    },

    searchJobs: async (searchTerm, filters = {}) => {
        const params = { search: searchTerm, ...filters };
        return await get().getJobs(params);
    },

    searchTests: async (searchTerm, filters = {}) => {
        const params = { search: searchTerm, ...filters };
        return await get().getTests(params);
    },

    // EXPORT UTILITIES
    //TODO: Add export for users
    exportUsers: async (filters = {}) => {
        set({ loading: true, error: null });
        try {
            const params = new URLSearchParams({ ...filters, export: 'true' });
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.USERS}/export?${params}`, {
                responseType: 'blob'
            });

            // Create download link
            const url = window.URL.createObjectURL(new Blob([res.data]));
            const link = document.createElement('a');
            link.href = url;
            link.download = `users-export-${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            return { success: true, message: 'Users exported successfully' };
        } catch (err) {
            return get().handleError(err, 'Export users failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add export for pending companies
    exportCompanies: async (filters = {}) => {
        set({ loading: true, error: null });
        try {
            const params = new URLSearchParams({ ...filters, export: 'true' });
            const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.COMPANIES}/export?${params}`, {
                responseType: 'blob'
            });

            const url = window.URL.createObjectURL(new Blob([res.data]));
            const link = document.createElement('a');
            link.href = url;
            link.download = `companies-export-${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            return { success: true, message: 'Companies exported successfully' };
        } catch (err) {
            return get().handleError(err, 'Export companies failed');
        } finally {
            set({ loading: false });
        }
    },

    // BULK OPERATIONS
    //TODO: Add bulk operations for jobs, tests, and companies
    bulkDeleteUsers: async (userIds) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.delete(`${ADMIN_ENDPOINTS.USERS}/bulk`, {
                data: { userIds }
            });

            // Update local state
            set((state) => ({
                users: state.users.filter(user => !userIds.includes(user._id)),
                statistics: {
                    ...state.statistics,
                    totalUsers: Math.max(0, state.statistics.totalUsers - userIds.length)
                }
            }));

            return { success: true, message: res.data.message, deleted: userIds.length };
        } catch (err) {
            return get().handleError(err, 'Bulk delete users failed');
        } finally {
            set({ loading: false });
        }
    },
    //TODO: Add bulk operations for jobs, tests, and companies
    bulkApproveCompanies: async (companyIds) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(`${ADMIN_ENDPOINTS.COMPANIES}/bulk-approve`, {
                companyIds
            });

            // Update local state
            set((state) => ({
                pendingCompanies: state.pendingCompanies.filter(company => !companyIds.includes(company._id)),
                statistics: {
                    ...state.statistics,
                    totalCompanies: state.statistics.totalCompanies + companyIds.length,
                    activeCompanies: state.statistics.activeCompanies + companyIds.length,
                    pendingCompanies: Math.max(0, state.statistics.pendingCompanies - companyIds.length)
                }
            }));

            return { success: true, message: res.data.message, approved: companyIds.length };
        } catch (err) {
            return get().handleError(err, 'Bulk approve companies failed');
        } finally {
            set({ loading: false });
        }
    },

    //Here we use Dummy data for AUDIT LOGS
    //TODO: Replace with actual API call
    getAuditLogs: async (params = {}) => {
        set({ loading: true, error: null });
        try {
            const queryParams = new URLSearchParams(params);
            // const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.SETTINGS}/audit-logs?${queryParams}`);
            const res = {
                "success": true,
                "data": {
                    "logs": [
                        {
                            "action": "User created",
                            "description": "User <NAME_EMAIL> was created",
                            "timestamp": "2025-01-10T10:00:00Z"
                        },
                        {
                            "action": "User updated",
                            "description": "User <NAME_EMAIL> was updated",
                            "timestamp": "2025-01-10T10:00:00Z"
                        }, {
                            "action": "User deleted",
                            "description": "User <NAME_EMAIL> was deleted",
                            "timestamp": "2025-01-10T10:00:00Z"
                        }, {
                            "action": "User created",
                            "description": "User <NAME_EMAIL> was created",
                            "timestamp": "2025-01-10T10:00:00Z"
                        }, {
                            "action": "User updated",
                            "description": "User <NAME_EMAIL> was updated",
                            "timestamp": "2025-01-10T10:00:00Z"
                        }, {
                            "action": "User deleted",
                            "description": "User <NAME_EMAIL> was deleted",
                            "timestamp": "2025-01-10T10:00:00Z"
                        }, {
                            "action": "User created",
                            "description": "User <NAME_EMAIL> was created",
                            "timestamp": "2025-01-10T10:00:00Z"
                        }, {
                            "action": "User updated",
                            "description": "User <NAME_EMAIL> was updated",
                            "timestamp": "2025-01-10T10:00:00Z"
                        }, {
                            "action": "User deleted",
                            "description": "User <NAME_EMAIL> was deleted",
                            "timestamp": "2025-01-10T10:00:00Z"
                        }

                    ],
                    "pagination": {
                        "current": 1,
                        "pages": 1,
                        "total": 10
                    }
                }
            }

            return { success: true, logs: res.data.logs, pagination: res.data.pagination };
        } catch (err) {
            return get().handleError(err, 'Fetch audit logs failed');
        } finally {
            set({ loading: false });
        }
    },

    //Here use Dummy data for SYSTEM HEALTH
    //TODO: Replace with actual API call
    getSystemHealth: async () => {
        set({ loading: true, error: null });
        try {
            // const res = await axiosInstance.get(`${ADMIN_ENDPOINTS.SETTINGS}/system-health`);
            const res = {
                "success": true,
                "health": {
                    "database": true,
                    "server": true,
                    "uptime": "12 hours",
                    "memoryUsage": "200MB",
                    "cpuUsage": "50%",
                    "diskUsage": "80%"
                }
            }
            return { success: true, health: res.health };
        } catch (err) {
            return get().handleError(err, 'Fetch system health failed');
        } finally {
            set({ loading: false });
        }
    },
    // UTILITY FUNCTIONS
    //TODO: Add loading state for refresh operation
    refreshData: async (forceRefresh = false) => {
        const state = get();

        // Prevent multiple simultaneous refresh operations
        if (state.isLoading('refreshing')) {
            console.log('Refresh already in progress, skipping...');
            return { success: false, message: 'Refresh already in progress' };
        }

        state.setLoadingState('refreshing', true);

        try {
            // Only refresh if cache is expired or force refresh is requested
            const promises = [];

            if (forceRefresh || !state.isCacheValid('users')) {
                promises.push(state.getUsers({ limit: 1000 }));
            }
            if (forceRefresh || !state.isCacheValid('jobs')) {
                promises.push(state.getJobs({ limit: 1000 }));
            }
            if (forceRefresh || !state.isCacheValid('tests')) {
                promises.push(state.getTests({ limit: 1000 }));
            }

            // Always refresh companies since they're derived from users
            if (promises.length > 0 || forceRefresh) {
                promises.push(state.getCompanies({ limit: 1000 }));
            }

            if (promises.length > 0) {
                await Promise.allSettled(promises);
                // Refresh dashboard stats after data is updated
                await state.getDashboardStats(forceRefresh);
            }

            return { success: true, refreshed: promises.length };
        } finally {
            state.setLoadingState('refreshing', false);
        }
    },

    // Smart refresh that only refreshes stale data
    //Todo: Add loading state for smart refresh
    smartRefresh: async () => {
        return await get().refreshData(false);
    },

    // Force refresh all data
    //Todo: Add loading state for force refresh
    forceRefresh: async () => {
        get().clearCache(); // Clear all cache
        return await get().refreshData(true);
    },

    resetState: () => {
        set({
            users: [],
            companies: [],
            pendingCompanies: [],
            jobPosts: [],
            tests: [],
            settings: null,
            loading: false,
            error: null,
            usersPagination: { current: 1, pages: 1, total: 0 },
            companiesPagination: { current: 1, pages: 1, total: 0 },
            jobsPagination: { current: 1, pages: 1, total: 0 },
            testsPagination: { current: 1, pages: 1, total: 0 },
            statistics: {
                totalUsers: 0,
                totalCompanies: 0,
                totalJobs: 0,
                totalTests: 0,
                activeUsers: 0,
                activeCompanies: 0,
                pendingCompanies: 0,
                flaggedJobs: 0,
                flaggedTests: 0
            }
        });
    }
}));

export default useAdminStore;