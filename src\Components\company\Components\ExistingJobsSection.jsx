// src/components/ExistingJobsSection.js
import React, { useState } from 'react';
import JobCard from './JobCard';
import JobTable from './JobTable';
import {
    Squares2X2Icon,
    TableCellsIcon,
    MagnifyingGlassIcon,
    FunnelIcon
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';

const ExistingJobsSection = ({
    jobs,
    onEdit,
    onStatusUpdate,
    onViewApplications,
    onViewDetails,
    searchQuery,
    setSearchQuery,
    filterCategory,
    setFilterCategory
}) => {
    const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'

    // Get unique categories for filter dropdown
    const categories = [...new Set(jobs?.map(job => job.category).filter(Boolean))];

    if (!jobs || jobs.length === 0) {
        return (
            <div className="text-center text-gray-500 mt-8">
                <div className="bg-gray-50 rounded-xl p-12">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Squares2X2Icon className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No jobs posted yet</h3>
                    <p className="text-gray-500">Create a new job to get started with your recruitment process.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="mt-8">
            {/* Header with Search, Filter, and View Toggle */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    {/* Left side - Search and Filter */}
                    <div className="flex flex-col sm:flex-row gap-4 flex-1">
                        {/* Search */}
                        <div className="relative flex-1 max-w-md">
                            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search jobs..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                            />
                        </div>

                        {/* Category Filter */}
                        <div className="relative">
                            <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <select
                                value={filterCategory}
                                onChange={(e) => setFilterCategory(e.target.value)}
                                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white min-w-[150px]"
                            >
                                <option value="">All Categories</option>
                                {categories.map(category => (
                                    <option key={category} value={category}>{category}</option>
                                ))}
                            </select>
                        </div>
                    </div>

                    {/* Right side - View Toggle */}
                    <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                        <button
                            onClick={() => setViewMode('cards')}
                            className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all ${
                                viewMode === 'cards'
                                    ? 'bg-white text-blue-600 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            <Squares2X2Icon className="w-4 h-4" />
                            Cards
                        </button>
                        <button
                            onClick={() => setViewMode('table')}
                            className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all ${
                                viewMode === 'table'
                                    ? 'bg-white text-blue-600 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            <TableCellsIcon className="w-4 h-4" />
                            Table
                        </button>
                    </div>
                </div>

                {/* Results count */}
                <div className="mt-4 pt-4 border-t border-gray-100">
                    <p className="text-sm text-gray-600">
                        Showing <span className="font-medium">{jobs.length}</span> job{jobs.length !== 1 ? 's' : ''}
                        {searchQuery && (
                            <span> matching "<span className="font-medium">{searchQuery}</span>"</span>
                        )}
                        {filterCategory && (
                            <span> in <span className="font-medium">{filterCategory}</span></span>
                        )}
                    </p>
                </div>
            </div>

            {/* Jobs Display */}
            <motion.div
                key={viewMode}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
            >
                {viewMode === 'cards' ? (
                    <div className="grid md:grid-cols-2 gap-6">
                        {jobs.map((job) => (
                            <JobCard
                                key={job._id}
                                job={job}
                                onEdit={onEdit}
                                onStatusUpdate={onStatusUpdate}
                                onViewApplications={onViewApplications}
                                onViewDetails={onViewDetails}
                            />
                        ))}
                    </div>
                ) : (
                    <JobTable
                        jobs={jobs}
                        onEdit={onEdit}
                        onStatusUpdate={onStatusUpdate}
                        onViewApplications={onViewApplications}
                        onViewDetails={onViewDetails}
                    />
                )}
            </motion.div>
        </div>
    );
};

export default ExistingJobsSection;