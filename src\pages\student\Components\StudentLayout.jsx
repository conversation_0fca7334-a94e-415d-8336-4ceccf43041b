import React from 'react';
import { Outlet, NavLink } from 'react-router-dom';
import StudentHeader from './Header';
import { FaUser, FaBriefcase, FaClipboardList, FaFlask, FaChartBar, FaHome, FaFileAlt } from 'react-icons/fa';

const navLinks = [
  { to: '/student/home', label: 'Home', icon: <FaHome /> },
  { to: '/student/profile', label: 'Profile', icon: <FaUser /> },
  { to: '/student/resume', label: 'Resume', icon: <FaFileAlt /> },
  { to: '/student/jobs', label: 'Jobs', icon: <FaBriefcase /> },
  { to: '/student/applications', label: 'Applications', icon: <FaClipboardList /> },
  { to: '/student/tests', label: 'Tests', icon: <FaFlask /> },
  { to: '/student/results', label: 'Results', icon: <FaChartBar /> },
];

const StudentLayout = () => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100">
    <StudentHeader />
    {/* Global Sidebar */}
    <aside className="fixed left-0 top-12 h-full w-64 bg-gradient-to-b from-[#23414b] to-gray-900 border-r border-gray-900 shadow-2xl flex flex-col p-0 z-30 overflow-y-auto">
      <div className="px-8 py-7 border-b border-gray-800 text-2xl font-extrabold text-white tracking-tight flex items-center gap-3">
        <span className="w-7 h-7 text-white opacity-80"><FaHome /></span>
        Menu
      </div>
      <nav className="flex flex-col gap-4 px-4 py-8 custom-scrollbar">
        {navLinks.map((link) => (
          <NavLink
            key={link.to}
            to={link.to}
            className={({ isActive }) =>
              `flex items-center gap-3 text-lg font-medium rounded-full px-6 py-3 transition-all duration-200 mb-2 ${isActive ? 'bg-white/10 text-white font-bold shadow-lg ring-2 ring-blue-400/40 scale-[1.03]' : 'text-gray-200 hover:bg-white/5 hover:text-white'}`
            }
          >
            <span className="text-xl">{link.icon}</span>
            <span>{link.label}</span>
          </NavLink>
        ))}
      </nav>
    </aside>
    <main className="pt-16 p-6 md:p-10 lg:p-16 transition-all duration-300 ml-64">
      <div className="w-full">
        <Outlet />
      </div>
    </main>
  </div>
);

export default StudentLayout; 