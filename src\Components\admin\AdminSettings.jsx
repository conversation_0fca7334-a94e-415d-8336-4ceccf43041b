import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Settings as SettingsIcon,
  Shield,
  Database,
  Activity,
  FileText,
  Download,
  Upload,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Building2,
  <PERSON>riefcase,
  BarChart3,
  Eye,
  Save,
  X
} from 'lucide-react';
import useAdminStore from '../../store/adminStore';

const AdminSettings = () => {
  // Admin store integration
  const {
    settings,
    loading,
    error,
    getSettings,
    updateSettings,
    getAuditLogs,
    getSystemHealth,
    exportUsers,
    exportCompanies,
    clearError
  } = useAdminStore();

  // Local state
  const [activeTab, setActiveTab] = useState('general');
  const [settingsForm, setSettingsForm] = useState({
    siteName: '',
    siteDescription: '',
    contactEmail: '',
    maxFileSize: 10,
    allowedFileTypes: ['pdf', 'doc', 'docx'],
    emailNotifications: true,
    maintenanceMode: false,
    registrationEnabled: true,
    autoApproveCompanies: false
  });
  const [auditLogs, setAuditLogs] = useState([]);
  const [systemHealth, setSystemHealth] = useState(null);
  const [showSaveConfirm, setShowSaveConfirm] = useState(false);

  // Load settings and system data on component mount
  useEffect(() => {
    const loadData = async () => {
      await getSettings();
      const healthResult = await getSystemHealth();
      if (healthResult.success) {
        setSystemHealth(healthResult.health);
      }

      const logsResult = await getAuditLogs({ limit: 10 });
      if (logsResult.success) {
        setAuditLogs(logsResult.logs);
      }
    };

    loadData();
  }, []);

  // Update form when settings are loaded
  useEffect(() => {
    if (settings) {
      setSettingsForm({
        siteName: settings.siteName || '',
        siteDescription: settings.siteDescription || '',
        contactEmail: settings.contactEmail || '',
        maxFileSize: settings.maxFileSize || 10,
        allowedFileTypes: settings.allowedFileTypes || ['pdf', 'doc', 'docx'],
        emailNotifications: settings.emailNotifications !== false,
        maintenanceMode: settings.maintenanceMode || false,
        registrationEnabled: settings.registrationEnabled !== false,
        autoApproveCompanies: settings.autoApproveCompanies || false
      });
    }
  }, [settings]);

  // Handler functions
  const handleFormChange = (field, value) => {
    setSettingsForm(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveSettings = async () => {
    try {
      const result = await updateSettings(settingsForm);
      if (result.success) {
        setShowSaveConfirm(true);
        setTimeout(() => setShowSaveConfirm(false), 3000);
      }
    } catch (error) {
      console.error('Save settings failed:', error);
    }
  };

  const handleExportData = async (type) => {
    try {
      if (type === 'users') {
        await exportUsers();
      } else if (type === 'companies') {
        await exportCompanies();
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const refreshSystemHealth = async () => {
    const result = await getSystemHealth();
    if (result.success) {
      setSystemHealth(result.health);
    }
  };

  const refreshAuditLogs = async () => {
    const result = await getAuditLogs({ limit: 10 });
    if (result.success) {
      setAuditLogs(result.logs);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.1 } }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 p-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div className="mb-8" variants={itemVariants}>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">System Settings</h1>
          <p className="text-gray-600">Configure system settings, monitor health, and manage data</p>
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div
            className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center gap-2">
              <AlertTriangle className="text-red-500" size={20} />
              <span className="text-red-700 font-medium">{error}</span>
              <button
                onClick={clearError}
                className="ml-auto text-red-500 hover:text-red-700"
              >
                <X size={16} />
              </button>
            </div>
          </motion.div>
        )}

        {/* Save Confirmation */}
        <AnimatePresence>
          {showSaveConfirm && (
            <motion.div
              className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              <div className="flex items-center gap-2">
                <CheckCircle className="text-green-500" size={20} />
                <span className="text-green-700 font-medium">Settings saved successfully!</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Tabs */}
        <motion.div className="mb-6" variants={itemVariants}>
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'general', label: 'General Settings', icon: SettingsIcon },
                { id: 'system', label: 'System Health', icon: Activity },
                { id: 'data', label: 'Data Management', icon: Database },
                { id: 'audit', label: 'Audit Logs', icon: FileText }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon size={16} />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </motion.div>

        {/* Tab Content */}
        <motion.div variants={itemVariants}>
          {activeTab === 'general' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">General Settings</h2>
                <button
                  onClick={handleSaveSettings}
                  disabled={loading}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  <Save size={16} />
                  Save Changes
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                  <input
                    type="text"
                    value={settingsForm.siteName}
                    onChange={(e) => handleFormChange('siteName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                  <input
                    type="email"
                    value={settingsForm.contactEmail}
                    onChange={(e) => handleFormChange('contactEmail', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                  <textarea
                    value={settingsForm.siteDescription}
                    onChange={(e) => handleFormChange('siteDescription', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Max File Size (MB)</label>
                  <input
                    type="number"
                    value={settingsForm.maxFileSize}
                    onChange={(e) => handleFormChange('maxFileSize', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Allowed File Types</label>
                  <input
                    type="text"
                    value={settingsForm.allowedFileTypes.join(', ')}
                    onChange={(e) => handleFormChange('allowedFileTypes', e.target.value.split(', '))}
                    placeholder="pdf, doc, docx"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="md:col-span-2 space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Email Notifications</label>
                      <p className="text-sm text-gray-500">Send email notifications for important events</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settingsForm.emailNotifications}
                      onChange={(e) => handleFormChange('emailNotifications', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Registration Enabled</label>
                      <p className="text-sm text-gray-500">Allow new user registrations</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settingsForm.registrationEnabled}
                      onChange={(e) => handleFormChange('registrationEnabled', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Auto-approve Companies</label>
                      <p className="text-sm text-gray-500">Automatically approve company registrations</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settingsForm.autoApproveCompanies}
                      onChange={(e) => handleFormChange('autoApproveCompanies', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Maintenance Mode</label>
                      <p className="text-sm text-gray-500">Put the site in maintenance mode</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settingsForm.maintenanceMode}
                      onChange={(e) => handleFormChange('maintenanceMode', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'system' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">System Health</h2>
                <button
                  onClick={refreshSystemHealth}
                  className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  <RefreshCw size={16} />
                  Refresh
                </button>
              </div>

              {systemHealth ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <Database className="text-blue-600" size={24} />
                      <div>
                        <p className="text-sm text-gray-600">Database</p>
                        <p className={`font-semibold ${systemHealth.database ? 'text-green-600' : 'text-red-600'}`}>
                          {systemHealth.database ? 'Connected' : 'Disconnected'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <Activity className="text-green-600" size={24} />
                      <div>
                        <p className="text-sm text-gray-600">Server Status</p>
                        <p className="font-semibold text-green-600">Online</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <Users className="text-purple-600" size={24} />
                      <div>
                        <p className="text-sm text-gray-600">Active Users</p>
                        <p className="font-semibold text-purple-600">{systemHealth.activeUsers || 0}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-orange-50 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <Clock className="text-orange-600" size={24} />
                      <div>
                        <p className="text-sm text-gray-600">Uptime</p>
                        <p className="font-semibold text-orange-600">{systemHealth.uptime || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">Loading system health data...</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'data' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Data Management</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Export Data</h3>

                  <button
                    onClick={() => handleExportData('users')}
                    className="w-full flex items-center gap-3 p-4 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <Download className="text-blue-600" size={20} />
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Export Users</p>
                      <p className="text-sm text-gray-500">Download all user data as CSV</p>
                    </div>
                  </button>

                  <button
                    onClick={() => handleExportData('companies')}
                    className="w-full flex items-center gap-3 p-4 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <Download className="text-green-600" size={20} />
                    <div className="text-left">
                      <p className="font-medium text-gray-900">Export Companies</p>
                      <p className="text-sm text-gray-500">Download all company data as CSV</p>
                    </div>
                  </button>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Import Data</h3>

                  <div className="p-4 border border-gray-300 rounded-lg">
                    <div className="flex items-center gap-3 mb-3">
                      <Upload className="text-purple-600" size={20} />
                      <p className="font-medium text-gray-900">Bulk Import</p>
                    </div>
                    <p className="text-sm text-gray-500 mb-3">Upload CSV files to import data</p>
                    <input
                      type="file"
                      accept=".csv"
                      className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'audit' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Audit Logs</h2>
                <button
                  onClick={refreshAuditLogs}
                  className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  <RefreshCw size={16} />
                  Refresh
                </button>
              </div>

              {auditLogs.length > 0 ? (
                <div className="space-y-3">
                  {auditLogs.map((log, index) => (
                    <div key={index} className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                          <Eye size={16} className="text-blue-600" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{log.action || 'System Action'}</p>
                        <p className="text-sm text-gray-500">{log.description || 'No description available'}</p>
                      </div>
                      <div className="text-sm text-gray-500">
                        {log.timestamp ? new Date(log.timestamp).toLocaleString() : 'Unknown time'}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">No audit logs available</p>
                </div>
              )}
            </div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default AdminSettings; 