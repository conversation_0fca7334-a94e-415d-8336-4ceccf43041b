import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  CloudArrowUpIcon,
  DocumentIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const QuestionUpload = ({ onUpload, loading = false, className = "" }) => {
  const [dragActive, setDragActive] = useState(false);
  const [fileName, setFileName] = useState('');
  const [uploadStatus, setUploadStatus] = useState(null); // 'success', 'error', null
  const [uploadResult, setUploadResult] = useState(null); // Store detailed upload results
  const fileInputRef = useRef(null);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = async (file) => {
    // Validate file type
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      '.xlsx',
      '.xls'
    ];
    
    const isValidType = validTypes.some(type => 
      file.type === type || file.name.toLowerCase().endsWith(type)
    );

    if (!isValidType) {
      toast.error('Please upload a valid Excel file (.xlsx or .xls)');
      setUploadStatus('error');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      setUploadStatus('error');
      return;
    }

    setFileName(file.name);
    setUploadStatus(null);
    setUploadResult(null);

    try {
      const result = await onUpload(file);
      setUploadResult(result);

      if (result.success) {
        setUploadStatus('success');
        const summary = result.summary;
        if (summary) {
          toast.success(
            `Successfully processed ${summary.totalRowsProcessed} rows. ` +
            `${summary.questionsInserted} questions inserted, ` +
            `${summary.duplicatesSkipped} duplicates skipped.`
          );
        } else {
          toast.success(result.message || `Successfully uploaded ${result.count} questions`);
        }
      } else {
        setUploadStatus('error');
        if (result.validationErrors && result.validationErrors.length > 0) {
          toast.error(`Validation failed for ${result.validationErrors.length} rows. Check the details below.`);
        } else if (result.expectedFormat) {
          toast.error('Invalid Excel format. Please check the required columns.');
        } else {
          toast.error(result.error || 'Failed to upload file');
        }
      }
    } catch (error) {
      setUploadStatus('error');
      setUploadResult({ success: false, error: 'An error occurred while uploading the file' });
      toast.error('An error occurred while uploading the file');
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const clearFile = () => {
    setFileName('');
    setUploadStatus(null);
    setUploadResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center min-w-[320px] min-h-[400px] ${className}`}
      initial={{ opacity: 0, y: 40, scale: 1.1 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 1.2, type: 'spring', stiffness: 60 }}
    >
      <h2 className="text-xl font-bold text-[rgb(35,65,75)] mb-6 text-center">
        Upload Aptitude Questions
      </h2>

      {/* Upload Area */}
      <div
        className={`relative w-full flex-1 border-2 border-dashed rounded-xl transition-all duration-200 ${
          dragActive
            ? 'border-blue-500 bg-blue-50'
            : uploadStatus === 'success'
            ? 'border-green-500 bg-green-50'
            : uploadStatus === 'error'
            ? 'border-red-500 bg-red-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".xlsx,.xls"
          onChange={handleFileInput}
          className="hidden"
        />

        <div className="flex flex-col items-center justify-center h-full p-6 text-center">
          {loading ? (
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
              <p className="text-gray-600">Processing file...</p>
            </div>
          ) : uploadStatus === 'success' ? (
            <div className="flex flex-col items-center">
              <CheckCircleIcon className="h-16 w-16 text-green-500 mb-4" />
              <p className="text-green-600 font-semibold mb-2">Upload Successful!</p>
              <p className="text-sm text-gray-600 mb-4">{fileName}</p>
              <button
                onClick={clearFile}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Upload Another File
              </button>
            </div>
          ) : uploadStatus === 'error' ? (
            <div className="flex flex-col items-center">
              <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mb-4" />
              <p className="text-red-600 font-semibold mb-2">Upload Failed</p>
              <p className="text-sm text-gray-600 mb-4">{fileName}</p>
              <button
                onClick={clearFile}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Try Again
              </button>
            </div>
          ) : fileName ? (
            <div className="flex flex-col items-center">
              <DocumentIcon className="h-16 w-16 text-blue-500 mb-4" />
              <p className="text-gray-800 font-semibold mb-2">File Selected</p>
              <p className="text-sm text-gray-600 mb-4">{fileName}</p>
              <div className="flex gap-2">
                <button
                  onClick={() => handleFile(fileInputRef.current.files[0])}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Upload
                </button>
                <button
                  onClick={clearFile}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <CloudArrowUpIcon className="h-16 w-16 text-gray-400 mb-4" />
              <p className="text-gray-600 font-semibold mb-2">
                Drag & drop your Excel file here
              </p>
              <p className="text-sm text-gray-500 mb-4">or</p>
              <button
                onClick={openFileDialog}
                className="px-6 py-3 bg-[rgb(35,65,75)] text-white rounded-lg font-semibold shadow hover:bg-[rgb(45,85,100)] transition-all"
              >
                Choose File
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Upload Results Details */}
      {uploadResult && (
        <div className="w-full mt-4">
          {uploadResult.success ? (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="text-sm font-semibold text-green-800 mb-2">Upload Summary</h3>
              {uploadResult.summary && (
                <div className="text-xs text-green-700 space-y-1">
                  <p>• Total rows processed: {uploadResult.summary.totalRowsProcessed}</p>
                  <p>• Questions inserted: {uploadResult.summary.questionsInserted}</p>
                  <p>• Duplicates skipped: {uploadResult.summary.duplicatesSkipped}</p>
                  <p>• Validation errors: {uploadResult.summary.validationErrors}</p>
                </div>
              )}
            </div>
          ) : (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <h3 className="text-sm font-semibold text-red-800 mb-2">Upload Failed</h3>
              <p className="text-xs text-red-700 mb-2">{uploadResult.error}</p>

              {uploadResult.validationErrors && uploadResult.validationErrors.length > 0 && (
                <div className="mt-3">
                  <h4 className="text-xs font-semibold text-red-800 mb-1">Validation Errors:</h4>
                  <div className="max-h-32 overflow-y-auto">
                    {uploadResult.validationErrors.slice(0, 5).map((error, index) => (
                      <div key={index} className="text-xs text-red-600 mb-1">
                        Row {error.row}: {error.errors.join(', ')}
                      </div>
                    ))}
                    {uploadResult.validationErrors.length > 5 && (
                      <p className="text-xs text-red-600 italic">
                        ... and {uploadResult.validationErrors.length - 5} more errors
                      </p>
                    )}
                  </div>
                </div>
              )}

              {uploadResult.expectedFormat && (
                <div className="mt-3">
                  <h4 className="text-xs font-semibold text-red-800 mb-1">Expected Format:</h4>
                  <div className="text-xs text-red-600">
                    <p>Required columns: {uploadResult.expectedFormat.requiredColumns?.join(', ')}</p>
                    <p>Supported types: {uploadResult.expectedFormat.supportedTypes?.join(', ')}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* File Format Info */}
      <div className="w-full mt-6">
        <h3 className="text-sm font-semibold text-gray-700 mb-3">Required Excel Format:</h3>

        {/* Required Columns */}
        <div className="mb-4">
          <h4 className="text-xs font-semibold text-gray-600 mb-2">Required Columns:</h4>
          <div className="grid grid-cols-2 gap-2">
            {[
              { label: 'Question', desc: 'Question text' },
              { label: 'Type', desc: 'MCQ, Multiple-Select, Short-Answer, Code' },
              { label: 'Category', desc: 'Frontend, Backend, Full Stack, etc.' },
              { label: 'Difficulty', desc: 'Easy, Medium, Hard' }
            ].map((field, index) => (
              <div key={index} className="flex items-center gap-2">
                <span className="px-2 py-1 rounded bg-blue-100 text-blue-800 font-semibold text-xs">
                  {field.label}
                </span>
                <span className="text-xs text-gray-500 truncate" title={field.desc}>
                  {field.desc}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Optional Columns */}
        <div className="mb-4">
          <h4 className="text-xs font-semibold text-gray-600 mb-2">Optional Columns:</h4>
          <div className="grid grid-cols-2 gap-2">
            {[
              { label: 'Option1', desc: 'First option (for MCQ)' },
              { label: 'Option2', desc: 'Second option (for MCQ)' },
              { label: 'Option3', desc: 'Third option (for MCQ)' },
              { label: 'Option4', desc: 'Fourth option (for MCQ)' },
              { label: 'Correct', desc: 'Correct answer' },
              { label: 'Explanation', desc: 'Answer explanation' },
              { label: 'Points', desc: 'Question points (default: 1)' }
            ].map((field, index) => (
              <div key={index} className="flex items-center gap-2">
                <span className="px-2 py-1 rounded bg-gray-100 text-gray-700 font-semibold text-xs">
                  {field.label}
                </span>
                <span className="text-xs text-gray-500 truncate" title={field.desc}>
                  {field.desc}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-xs text-blue-800">
            <strong>Supported Categories:</strong> Frontend, Backend, Full Stack, Data Science, DevOps, Mobile, UI/UX, QA, Aptitude, Logical, Other
          </p>
        </div>

        <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-xs text-green-800">
            <strong>Supported Types:</strong> MCQ (Multiple Choice), Multiple-Select, Short-Answer, Code
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default QuestionUpload;
