import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import useAuthStore from '../../store/authStore';

/**
 * RoleBasedRedirect component that redirects users to their appropriate dashboard
 * based on their role. This is used for the root path and other generic redirects.
 */
const RoleBasedRedirect = () => {
  const { user, isAuthenticated, loading, initialized, initialize } = useAuthStore();

  useEffect(() => {
    if (!initialized) {
      initialize();
    }
  }, [initialized, initialize]);

  // Show loading spinner while checking authentication
  if (!initialized || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-200">
        <div className="text-center">
          <DotLottieReact
            src="https://lottie.host/454c3626-2618-4344-b957-5f9c8d674a99/UVood7R6b1.lottie"
            loop
            autoplay
            style={{ width: 200, height: 200 }}
          />
          <p className="mt-4 text-gray-600 font-medium">Loading...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // If authenticated, redirect based on role
  const dashboardPath = getDashboardPath(user?.role);
  return <Navigate to={dashboardPath} replace />;
};

/**
 * Get the appropriate dashboard path based on user role
 * @param {string} role - User role (admin, company, student, candidate)
 * @returns {string} Dashboard path
 */
const getDashboardPath = (role) => {
  switch (role) {
    case 'admin':
      return '/admin-dashboard';
    case 'company':
      return '/dashboard';
    case 'student':
    case 'candidate':
      return '/student/home';
    default:
      return '/login';
  }
};

export default RoleBasedRedirect;
