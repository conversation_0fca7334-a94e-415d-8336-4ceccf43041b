import React from 'react';
import {
    EnvelopeIcon,
    PhoneIcon,
    MapPinIcon,
    GlobeAltIcon,
    BriefcaseIcon,
    AcademicCapIcon,
    TrophyIcon,
    DocumentTextIcon,
    StarIcon,
    LanguageIcon,
    HeartIcon,
    BookOpenIcon,
    UserGroupIcon,
    LinkIcon,
    CalendarIcon,
    BuildingOfficeIcon
} from '@heroicons/react/24/outline';

const ResumeTemplate = ({ resume }) => {
    if (!resume) {
        return (
            <div className="text-center py-12">
                <DocumentTextIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Resume Available</h3>
                <p className="text-gray-500">This candidate hasn't uploaded a resume yet.</p>
            </div>
        );
    }

    return (
        <div className="max-w-4xl mx-auto bg-white">
            {/* Header */}
            <div className="bg-gradient-to-r from-gray-900 to-gray-700 text-white p-8 rounded-t-lg">
                <div className="flex items-start gap-6">
                    {resume.ProfilePic ? (
                        <img 
                            src={resume.ProfilePic} 
                            alt={resume.Title}
                            className="w-24 h-24 rounded-full object-cover border-4 border-white"
                        />
                    ) : (
                        <div className="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-3xl font-bold">
                            {resume.Title?.charAt(0)?.toUpperCase() || 'R'}
                        </div>
                    )}
                    
                    <div className="flex-1">
                        <h1 className="text-3xl font-bold mb-2">{resume.Title}</h1>
                        {resume.Headline && (
                            <p className="text-xl text-gray-200 mb-4">{resume.Headline}</p>
                        )}
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {resume.Email && (
                                <div className="flex items-center gap-2">
                                    <EnvelopeIcon className="w-4 h-4" />
                                    <span>{resume.Email}</span>
                                </div>
                            )}
                            {resume.Phone && (
                                <div className="flex items-center gap-2">
                                    <PhoneIcon className="w-4 h-4" />
                                    <span>{resume.Phone}</span>
                                </div>
                            )}
                            {resume.Location && (
                                <div className="flex items-center gap-2">
                                    <MapPinIcon className="w-4 h-4" />
                                    <span>{resume.Location}</span>
                                </div>
                            )}
                            {resume.Website && (
                                <div className="flex items-center gap-2">
                                    <GlobeAltIcon className="w-4 h-4" />
                                    <a href={resume.Website} target="_blank" rel="noopener noreferrer" className="hover:text-gray-300">
                                        {resume.Website}
                                    </a>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            <div className="p-8 space-y-8">
                {/* Summary */}
                {resume.summery && (
                    <section>
                        <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-blue-500 pb-2">
                            Summary
                        </h2>
                        <p className="text-gray-700 leading-relaxed">{resume.summery}</p>
                    </section>
                )}

                {/* Experience */}
                {resume.Experience && resume.Experience.length > 0 && (
                    <section>
                        <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-blue-500 pb-2 flex items-center gap-2">
                            <BriefcaseIcon className="w-6 h-6" />
                            Experience
                        </h2>
                        <div className="space-y-6">
                            {resume.Experience.map((exp, index) => (
                                <div key={index} className="relative pl-8 border-l-2 border-gray-200">
                                    <div className="absolute -left-2 top-0 w-4 h-4 bg-blue-500 rounded-full"></div>
                                    <div className="bg-gray-50 rounded-lg p-6">
                                        <div className="flex justify-between items-start mb-3">
                                            <div>
                                                <h3 className="text-xl font-semibold text-gray-900">{exp.Position}</h3>
                                                <p className="text-lg text-blue-600 font-medium">{exp.Company}</p>
                                                <p className="text-gray-600">{exp.Location}</p>
                                            </div>
                                            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                                {new Date(exp.StartDate).toLocaleDateString()} - {
                                                    exp.EndDate ? new Date(exp.EndDate).toLocaleDateString() : 'Present'
                                                }
                                            </span>
                                        </div>
                                        {exp.Description && (
                                            <p className="text-gray-700 leading-relaxed">{exp.Description}</p>
                                        )}
                                        {exp.Website && (
                                            <a 
                                                href={exp.Website} 
                                                target="_blank" 
                                                rel="noopener noreferrer"
                                                className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 mt-2"
                                            >
                                                <LinkIcon className="w-4 h-4" />
                                                Company Website
                                            </a>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </section>
                )}

                {/* Education */}
                {resume.Education && resume.Education.length > 0 && (
                    <section>
                        <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-green-500 pb-2 flex items-center gap-2">
                            <AcademicCapIcon className="w-6 h-6" />
                            Education
                        </h2>
                        <div className="space-y-4">
                            {resume.Education.map((edu, index) => (
                                <div key={index} className="bg-green-50 rounded-lg p-6">
                                    <div className="flex justify-between items-start">
                                        <div>
                                            <h3 className="text-xl font-semibold text-gray-900">{edu.Degree}</h3>
                                            <p className="text-lg text-green-600 font-medium">{edu.Institution}</p>
                                            <p className="text-gray-600">{edu.Location}</p>
                                        </div>
                                        <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                            {new Date(edu.StartDate).toLocaleDateString()} - {
                                                edu.EndDate ? new Date(edu.EndDate).toLocaleDateString() : 'Present'
                                            }
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </section>
                )}

                {/* Skills */}
                {resume.Skills && resume.Skills.length > 0 && (
                    <section>
                        <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-purple-500 pb-2 flex items-center gap-2">
                            <StarIcon className="w-6 h-6" />
                            Skills
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {resume.Skills.map((skill, index) => (
                                <div key={index} className="bg-purple-50 rounded-lg p-4">
                                    <div className="flex justify-between items-center mb-2">
                                        <h3 className="font-semibold text-gray-900">{skill.Skill}</h3>
                                        <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">
                                            {skill.Proficiency}
                                        </span>
                                    </div>
                                    {skill.Keywords && skill.Keywords.length > 0 && (
                                        <div className="flex flex-wrap gap-1">
                                            {skill.Keywords.map((keyword, kIndex) => (
                                                <span key={kIndex} className="bg-purple-200 text-purple-800 px-2 py-1 rounded-full text-xs">
                                                    {keyword}
                                                </span>
                                            ))}
                                        </div>
                                    )}
                                    {skill.Description && (
                                        <p className="text-gray-600 text-sm mt-2">{skill.Description}</p>
                                    )}
                                </div>
                            ))}
                        </div>
                    </section>
                )}

                {/* Projects */}
                {resume.Projects && resume.Projects.length > 0 && (
                    <section>
                        <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-orange-500 pb-2 flex items-center gap-2">
                            <DocumentTextIcon className="w-6 h-6" />
                            Projects
                        </h2>
                        <div className="space-y-4">
                            {resume.Projects.map((project, index) => (
                                <div key={index} className="bg-orange-50 rounded-lg p-6">
                                    <div className="flex justify-between items-start mb-3">
                                        <div>
                                            <h3 className="text-xl font-semibold text-gray-900">{project.Title}</h3>
                                            {project.Link && (
                                                <a 
                                                    href={project.Link} 
                                                    target="_blank" 
                                                    rel="noopener noreferrer"
                                                    className="text-orange-600 hover:text-orange-800 flex items-center gap-1"
                                                >
                                                    <LinkIcon className="w-4 h-4" />
                                                    View Project
                                                </a>
                                            )}
                                        </div>
                                        <span className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                                            {new Date(project.StartDate).toLocaleDateString()} - {
                                                project.EndDate ? new Date(project.EndDate).toLocaleDateString() : 'Ongoing'
                                            }
                                        </span>
                                    </div>
                                    {project.Description && (
                                        <p className="text-gray-700 mb-3">{project.Description}</p>
                                    )}
                                    {project.Technologies && project.Technologies.length > 0 && (
                                        <div className="flex flex-wrap gap-2">
                                            {project.Technologies.map((tech, tIndex) => (
                                                <span key={tIndex} className="bg-orange-200 text-orange-800 px-2 py-1 rounded text-sm">
                                                    {tech}
                                                </span>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </section>
                )}

                {/* Two Column Layout for smaller sections */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Certifications */}
                    {resume.Certifications && resume.Certifications.length > 0 && (
                        <section>
                            <h2 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-yellow-500 pb-2 flex items-center gap-2">
                                <TrophyIcon className="w-5 h-5" />
                                Certifications
                            </h2>
                            <div className="space-y-3">
                                {resume.Certifications.map((cert, index) => (
                                    <div key={index} className="bg-yellow-50 rounded-lg p-4">
                                        <h3 className="font-semibold text-gray-900">{cert.Title}</h3>
                                        <p className="text-yellow-600 font-medium">{cert.Issuer}</p>
                                        <p className="text-gray-600 text-sm">
                                            {new Date(cert.Date).toLocaleDateString()}
                                        </p>
                                        {cert.Website && (
                                            <a 
                                                href={cert.Website} 
                                                target="_blank" 
                                                rel="noopener noreferrer"
                                                className="text-yellow-600 hover:text-yellow-800 text-sm flex items-center gap-1 mt-1"
                                            >
                                                <LinkIcon className="w-3 h-3" />
                                                Verify
                                            </a>
                                        )}
                                        {cert.Description && (
                                            <p className="text-gray-600 text-sm mt-2">{cert.Description}</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </section>
                    )}

                    {/* Languages */}
                    {resume.Languages && resume.Languages.length > 0 && (
                        <section>
                            <h2 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-indigo-500 pb-2 flex items-center gap-2">
                                <LanguageIcon className="w-5 h-5" />
                                Languages
                            </h2>
                            <div className="space-y-3">
                                {resume.Languages.map((lang, index) => (
                                    <div key={index} className="bg-indigo-50 rounded-lg p-4">
                                        <div className="flex justify-between items-center">
                                            <h3 className="font-semibold text-gray-900">{lang.Name}</h3>
                                            <span className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-sm">
                                                {lang.Proficiency}
                                            </span>
                                        </div>
                                        {lang.Description && (
                                            <p className="text-gray-600 text-sm mt-2">{lang.Description}</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </section>
                    )}
                </div>

                {/* Additional sections in grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Awards */}
                    {resume.Awards && resume.Awards.length > 0 && (
                        <section>
                            <h2 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-red-500 pb-2 flex items-center gap-2">
                                <TrophyIcon className="w-5 h-5" />
                                Awards
                            </h2>
                            <div className="space-y-3">
                                {resume.Awards.map((award, index) => (
                                    <div key={index} className="bg-red-50 rounded-lg p-4">
                                        <h3 className="font-semibold text-gray-900">{award.Title}</h3>
                                        <p className="text-red-600 font-medium">{award.Issuer}</p>
                                        <p className="text-gray-600 text-sm">
                                            {new Date(award.Date).toLocaleDateString()}
                                        </p>
                                        {award.Description && (
                                            <p className="text-gray-600 text-sm mt-2">{award.Description}</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </section>
                    )}

                    {/* Publications */}
                    {resume.Publications && resume.Publications.length > 0 && (
                        <section>
                            <h2 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-teal-500 pb-2 flex items-center gap-2">
                                <BookOpenIcon className="w-5 h-5" />
                                Publications
                            </h2>
                            <div className="space-y-3">
                                {resume.Publications.map((pub, index) => (
                                    <div key={index} className="bg-teal-50 rounded-lg p-4">
                                        <h3 className="font-semibold text-gray-900">{pub.Title}</h3>
                                        <p className="text-teal-600 font-medium">{pub.Publisher}</p>
                                        <p className="text-gray-600 text-sm">
                                            {new Date(pub.Date).toLocaleDateString()}
                                        </p>
                                        {pub.Website && (
                                            <a 
                                                href={pub.Website} 
                                                target="_blank" 
                                                rel="noopener noreferrer"
                                                className="text-teal-600 hover:text-teal-800 text-sm flex items-center gap-1 mt-1"
                                            >
                                                <LinkIcon className="w-3 h-3" />
                                                Read Publication
                                            </a>
                                        )}
                                        {pub.Description && (
                                            <p className="text-gray-600 text-sm mt-2">{pub.Description}</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </section>
                    )}
                </div>

                {/* Social Profiles */}
                {resume.Profiles && resume.Profiles.length > 0 && (
                    <section>
                        <h2 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-blue-500 pb-2 flex items-center gap-2">
                            <LinkIcon className="w-5 h-5" />
                            Social Profiles
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {resume.Profiles.map((profile, index) => (
                                <a
                                    key={index}
                                    href={profile.ProfileLink}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="bg-blue-50 rounded-lg p-4 hover:bg-blue-100 transition-colors"
                                >
                                    <div className="flex items-center gap-3">
                                        {profile.ProfileImage && (
                                            <img 
                                                src={profile.ProfileImage} 
                                                alt={profile.Network}
                                                className="w-8 h-8"
                                                onError={(e) => {
                                                    e.target.style.display = 'none';
                                                }}
                                            />
                                        )}
                                        <div>
                                            <h3 className="font-semibold text-gray-900">{profile.Network}</h3>
                                            <p className="text-blue-600 text-sm">{profile.Username}</p>
                                        </div>
                                    </div>
                                </a>
                            ))}
                        </div>
                    </section>
                )}
            </div>
        </div>
    );
};

export default ResumeTemplate;
