import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon, PlusIcon, CheckIcon, TrashIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const ManualQuestionForm = ({
  isOpen,
  onClose,
  onSubmit,
  editingQuestion = null
}) => {
  const [formData, setFormData] = useState({
    questionText: '',
    questionType: 'MCQ',
    category: '',
    difficulty: 'Medium',
    options: [
      { text: '', isCorrect: false },
      { text: '', isCorrect: false }
    ],
    correctAnswer: '',
    explanation: '',
    points: 1
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Schema constants
  const QUESTION_TYPES = ['MCQ', 'Multiple-Select', 'Short-Answer', 'Code'];
  const CATEGORIES = ['Frontend', 'Backend', 'Full Stack', 'Data Science', 'DevOps', 'Mobile', 'UI/UX', 'QA', 'Aptitude', 'Logical', 'Other'];
  const DIFFICULTIES = ['Easy', 'Medium', 'Hard'];

  // Initialize form data
  useEffect(() => {
    if (editingQuestion) {
      // Convert old format options to new format if needed
      let options = [];
      if (editingQuestion.options && Array.isArray(editingQuestion.options)) {
        options = editingQuestion.options;
      } else {
        // Convert from old option1, option2, etc. format
        for (let i = 1; i <= 4; i++) {
          const optionText = editingQuestion[`option${i}`];
          if (optionText) {
            options.push({
              text: optionText,
              isCorrect: optionText === (editingQuestion.correctAnswer || editingQuestion.answer)
            });
          }
        }
      }

      setFormData({
        questionText: editingQuestion.questionText || editingQuestion.question || '',
        questionType: editingQuestion.questionType || 'MCQ',
        category: editingQuestion.category || '',
        difficulty: editingQuestion.difficulty || 'Medium',
        options: options.length >= 2 ? options : [
          { text: '', isCorrect: false },
          { text: '', isCorrect: false }
        ],
        correctAnswer: editingQuestion.correctAnswer || editingQuestion.answer || '',
        explanation: editingQuestion.explanation || '',
        points: editingQuestion.points || 1
      });
    }
  }, [editingQuestion, isOpen]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({
        questionText: '',
        questionType: 'MCQ',
        category: '',
        difficulty: 'Medium',
        options: [
          { text: '', isCorrect: false },
          { text: '', isCorrect: false }
        ],
        correctAnswer: '',
        explanation: '',
        points: 1
      });
      setErrors({});
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Question text validation (matches backend)
    if (!formData.questionText || formData.questionText.trim().length < 5) {
      newErrors.questionText = 'Question text must be at least 5 characters long';
    }
    if (formData.questionText && formData.questionText.length > 1000) {
      newErrors.questionText = 'Question text must not exceed 1000 characters';
    }

    // Question type validation
    if (!formData.questionType || !QUESTION_TYPES.includes(formData.questionType)) {
      newErrors.questionType = 'Valid question type is required';
    }

    // Category validation
    if (!formData.category || formData.category.trim().length < 2) {
      newErrors.category = 'Category is required';
    }

    // Difficulty validation
    if (!formData.difficulty || !DIFFICULTIES.includes(formData.difficulty)) {
      newErrors.difficulty = 'Valid difficulty level is required';
    }

    // Points validation
    if (formData.points && (formData.points < 1 || formData.points > 100)) {
      newErrors.points = 'Points must be between 1 and 100';
    }

    // Explanation validation
    if (formData.explanation && formData.explanation.length > 500) {
      newErrors.explanation = 'Explanation must not exceed 500 characters';
    }

    // MCQ/Multiple-Select specific validation
    if (formData.questionType === 'MCQ' || formData.questionType === 'Multiple-Select') {
      const validOptions = formData.options.filter(opt => opt.text && opt.text.trim());
      if (validOptions.length < 2) {
        newErrors.options = 'MCQ/Multiple-Select questions must have at least 2 options';
      }

      const hasCorrect = formData.options.some(opt => opt.isCorrect === true);
      if (!hasCorrect) {
        newErrors.options = 'At least one correct option must be specified';
      }

      // For MCQ, only one option should be correct
      if (formData.questionType === 'MCQ') {
        const correctCount = formData.options.filter(opt => opt.isCorrect === true).length;
        if (correctCount > 1) {
          newErrors.options = 'MCQ questions can have only one correct answer';
        }
      }
    }

    // Short-Answer/Code specific validation
    if ((formData.questionType === 'Short-Answer' || formData.questionType === 'Code') && !formData.correctAnswer.trim()) {
      newErrors.correctAnswer = 'Correct answer is required for Short-Answer and Code questions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Helper functions for managing options
  const addOption = () => {
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, { text: '', isCorrect: false }]
    }));
  };

  const removeOption = (index) => {
    if (formData.options.length <= 2) {
      toast.error('Minimum 2 options required');
      return;
    }
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }));
  };

  const updateOption = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((option, i) =>
        i === index ? { ...option, [field]: value } : option
      )
    }));
  };

  const toggleCorrectOption = (index) => {
    setFormData(prev => {
      const newOptions = [...prev.options];

      // For MCQ, only one option can be correct
      if (prev.questionType === 'MCQ') {
        newOptions.forEach((option, i) => {
          option.isCorrect = i === index;
        });
      } else {
        // For Multiple-Select, toggle the option
        newOptions[index].isCorrect = !newOptions[index].isCorrect;
      }

      return { ...prev, options: newOptions };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors before submitting');
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data according to backend schema
      const questionData = {
        questionText: formData.questionText.trim(),
        questionType: formData.questionType,
        category: formData.category,
        difficulty: formData.difficulty,
        points: parseInt(formData.points) || 1
      };

      // Add explanation if provided
      if (formData.explanation && formData.explanation.trim()) {
        questionData.explanation = formData.explanation.trim();
      }

      // Add options for MCQ/Multiple-Select
      if (formData.questionType === 'MCQ' || formData.questionType === 'Multiple-Select') {
        questionData.options = formData.options
          .filter(opt => opt.text && opt.text.trim())
          .map(opt => ({
            text: opt.text.trim(),
            isCorrect: opt.isCorrect
          }));
      }

      // Add correctAnswer for Short-Answer/Code
      if (formData.questionType === 'Short-Answer' || formData.questionType === 'Code') {
        questionData.correctAnswer = formData.correctAnswer.trim();
      }

      const result = await onSubmit(questionData);

      if (result?.success !== false) {
        toast.success(editingQuestion ? 'Question updated successfully' : 'Question added successfully');
        onClose();
      } else {
        // Handle backend validation errors
        if (result?.details && Array.isArray(result.details)) {
          result.details.forEach(error => toast.error(error));
        } else {
          toast.error(result?.error || 'Failed to save question');
        }
      }
    } catch (error) {
      toast.error('An error occurred while saving the question');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderTextField = (label, field, options = {}) => {
    const { placeholder, isTextarea = false, maxLength, rows = 3, required = false } = options;
    const error = errors[field];
    const value = formData[field];

    return (
      <div className="space-y-2">
        <label className="block text-sm font-semibold text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
          {maxLength && (
            <span className="text-xs text-gray-500 ml-2">
              ({value?.length || 0}/{maxLength})
            </span>
          )}
        </label>
        {isTextarea ? (
          <textarea
            value={value}
            onChange={(e) => handleChange(field, e.target.value)}
            placeholder={placeholder}
            rows={rows}
            maxLength={maxLength}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent transition-all resize-none ${
              error ? 'border-red-300 bg-red-50' : 'border-gray-300'
            }`}
          />
        ) : (
          <input
            type="text"
            value={value}
            onChange={(e) => handleChange(field, e.target.value)}
            placeholder={placeholder}
            maxLength={maxLength}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent transition-all ${
              error ? 'border-red-300 bg-red-50' : 'border-gray-300'
            }`}
          />
        )}
        {error && (
          <p className="text-red-600 text-sm flex items-center gap-1">
            <span>⚠️</span> {error}
          </p>
        )}
      </div>
    );
  };

  const renderSelectField = (label, field, options, required = false) => {
    const error = errors[field];
    const value = formData[field];

    return (
      <div className="space-y-2">
        <label className="block text-sm font-semibold text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <select
          value={value}
          onChange={(e) => handleChange(field, e.target.value)}
          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-transparent transition-all ${
            error ? 'border-red-300 bg-red-50' : 'border-gray-300'
          }`}
        >
          <option value="">Select {label.toLowerCase()}...</option>
          {options.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
        {error && (
          <p className="text-red-600 text-sm flex items-center gap-1">
            <span>⚠️</span> {error}
          </p>
        )}
      </div>
    );
  };

  const renderNumberField = (label, field, options = {}) => {
    const { min = 1, max = 100, required = false } = options;
    const error = errors[field];
    const value = formData[field];

    return (
      <div className="space-y-2">
        <label className="block text-sm font-semibold text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <input
          type="number"
          value={value}
          onChange={(e) => handleChange(field, parseInt(e.target.value) || 1)}
          min={min}
          max={max}
          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all ${
            error ? 'border-red-300 bg-red-50' : 'border-gray-300'
          }`}
        />
        {error && (
          <p className="text-red-600 text-sm flex items-center gap-1">
            <span>⚠️</span> {error}
          </p>
        )}
      </div>
    );
  };

  const renderOptionsSection = () => {
    const error = errors.options;

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <label className="block text-sm font-semibold text-gray-700">
            Answer Options <span className="text-red-500">*</span>
            <span className="text-xs text-gray-500 ml-2">
              (Mark correct {formData.questionType === 'MCQ' ? 'answer' : 'answers'})
            </span>
          </label>
          <button
            type="button"
            onClick={addOption}
            className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="h-3 w-3" />
            Add Option
          </button>
        </div>

        <div className="space-y-3">
          {formData.options.map((option, index) => (
            <div key={index} className="flex items-center gap-3">
              <div className="flex items-center">
                <input
                  type={formData.questionType === 'MCQ' ? 'radio' : 'checkbox'}
                  name="correctOption"
                  checked={option.isCorrect}
                  onChange={() => toggleCorrectOption(index)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
              </div>
              <div className="flex-1">
                <input
                  type="text"
                  value={option.text}
                  onChange={(e) => updateOption(index, 'text', e.target.value)}
                  placeholder={`Option ${index + 1}`}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              {formData.options.length > 2 && (
                <button
                  type="button"
                  onClick={() => removeOption(index)}
                  className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  title="Remove option"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              )}
            </div>
          ))}
        </div>

        {error && (
          <p className="text-red-600 text-sm flex items-center gap-1">
            <span>⚠️</span> {error}
          </p>
        )}

        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Tip:</strong> {formData.questionType === 'MCQ'
              ? 'Select one correct answer by clicking the radio button.'
              : 'Select multiple correct answers by checking the checkboxes.'
            }
          </p>
        </div>
      </div>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={(e) => e.target === e.currentTarget && onClose()}
        >
          <motion.div
            className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl h-[90vh] flex flex-col overflow-hidden"
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ duration: 0.3, type: 'spring', stiffness: 300, damping: 30 }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-800">
                {editingQuestion ? 'Edit Question' : 'Add New Question'}
              </h2>
              <button
                onClick={onClose}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="flex-1 p-6 space-y-6 overflow-y-auto min-h-0">
              {/* Question Text */}
              {renderTextField(
                'Question Text',
                'questionText',
                {
                  placeholder: 'Enter your question here...',
                  isTextarea: true,
                  maxLength: 1000,
                  rows: 4,
                  required: true
                }
              )}

              {/* Question Type and Category */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderSelectField('Question Type', 'questionType', QUESTION_TYPES, true)}
                {renderSelectField('Category', 'category', CATEGORIES, true)}
              </div>

              {/* Difficulty and Points */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderSelectField('Difficulty', 'difficulty', DIFFICULTIES, true)}
                {renderNumberField('Points', 'points', { min: 1, max: 100 })}
              </div>

              {/* Conditional Fields based on Question Type */}
              {(formData.questionType === 'MCQ' || formData.questionType === 'Multiple-Select') && (
                renderOptionsSection()
              )}

              {(formData.questionType === 'Short-Answer' || formData.questionType === 'Code') && (
                renderTextField(
                  'Correct Answer',
                  'correctAnswer',
                  {
                    placeholder: 'Enter the correct answer...',
                    isTextarea: formData.questionType === 'Code',
                    rows: formData.questionType === 'Code' ? 6 : 2,
                    required: true
                  }
                )
              )}

              {/* Explanation (Optional) */}
              {renderTextField(
                'Explanation',
                'explanation',
                {
                  placeholder: 'Optional explanation for the answer...',
                  isTextarea: true,
                  maxLength: 500,
                  rows: 3
                }
              )}

              {/* Question Type Helper */}
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <strong>Question Types:</strong>
                </p>
                <ul className="text-xs text-yellow-700 mt-1 space-y-1">
                  <li><strong>MCQ:</strong> Single correct answer from multiple options</li>
                  <li><strong>Multiple-Select:</strong> Multiple correct answers from options</li>
                  <li><strong>Short-Answer:</strong> Brief text response</li>
                  <li><strong>Code:</strong> Programming/code-based answer</li>
                </ul>
              </div>
            </form>

            {/* Footer */}
            <div className="flex-shrink-0 flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="flex items-center gap-2 px-6 py-3 bg-[rgb(35,65,75)] text-white rounded-lg hover:bg-[rgb(45,85,100)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px]"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    {editingQuestion ? 'Updating...' : 'Adding...'}
                  </>
                ) : (
                  <>
                    {editingQuestion ? <CheckIcon className="h-4 w-4" /> : <PlusIcon className="h-4 w-4" />}
                    {editingQuestion ? 'Update Question' : 'Add Question'}
                  </>
                )}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ManualQuestionForm;
