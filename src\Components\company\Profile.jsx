import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  FaEdit,
  FaCheckCircle,
  FaTimesCircle,
  FaTimes,
  FaEye,
  FaSave,
  FaSpinner,
  FaBuilding,
  FaMapMarkerAlt,
  FaUser,
  FaShare,
  FaImage,
  FaFileAlt
} from "react-icons/fa";
import useCompanyStore from "../../store/companyStore";
import { validateCompanyForm, INDUSTRY_OPTIONS, COMPANY_SIZE_OPTIONS } from "../../utils/companyValidation";



const initialState = {
  companyName: "",
  companyEmail: "",
  website: "",
  description: "",
  industry: "",
  companySize: "",
  logo: null,
  address: "",
  city: "",
  state: "",
  country: "",
  pincode: "",
  contactPersonName: "",
  contactPersonDesignation: "",
  contactPersonPhone: "",
  linkedin: "",
  twitter: "",
  facebook: "",
  github: "",
  verificationDocument: null,
};

const InputField = ({
  label,
  name,
  value,
  onChange,
  required = false,
  type = "text",
  placeholder = "",
  icon: Icon,
  className = ""
}) => (
  <div className={className}>
    <label className="block text-sm font-semibold text-gray-700 mb-2">
      {Icon && <Icon className="inline mr-2 text-blue-600" />}
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <input
      name={name}
      type={type}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400"
      required={required}
    />
  </div>
);

const TextAreaField = ({ label, name, value, onChange, placeholder = "", rows = 4 }) => (
  <div>
    <label className="block text-sm font-semibold text-gray-700 mb-2">
      {label}
    </label>
    <textarea
      name={name}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      rows={rows}
      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 resize-vertical"
    />
  </div>
);

const SelectField = ({ label, name, value, onChange, options, required = false, icon: Icon }) => (
  <div>
    <label className="block text-sm font-semibold text-gray-700 mb-2">
      {Icon && <Icon className="inline mr-2 text-blue-600" />}
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <select
      name={name}
      value={value}
      onChange={onChange}
      required={required}
      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 bg-white"
    >
      <option value="">Select {label.toLowerCase()}</option>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  </div>
);

const FileUpload = ({ label, name, onChange, accept, preview, icon: Icon }) => (
  <div>
    <label className="block text-sm font-semibold text-gray-700 mb-2">
      {Icon && <Icon className="inline mr-2 text-blue-600" />}
      {label}
    </label>
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors">
      <input
        name={name}
        type="file"
        accept={accept}
        onChange={onChange}
        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
      />
      {preview && (
        <div className="mt-4">
          {name === "logo" ? (
            <img src={preview} alt="Logo Preview" className="h-20 w-20 object-cover rounded-lg border shadow-sm" />
          ) : (
            <a
              href={preview}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
            >
              <FaEye className="mr-2" /> View Document
            </a>
          )}
        </div>
      )}
    </div>
  </div>
);

const SectionHeader = ({ title, icon: Icon }) => (
  <div className="flex items-center mb-6 pb-3 border-b border-gray-200">
    <Icon className="text-blue-600 mr-3 text-xl" />
    <h3 className="text-xl font-bold text-gray-800">{title}</h3>
  </div>
);

const StatusModal = ({ isOpen, onClose, status, message }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 backdrop-blur-sm">
      <div className="bg-white max-w-md w-full mx-4 rounded-xl shadow-2xl p-8 relative transform transition-all">
        <button
          className="absolute right-4 top-4 text-gray-400 hover:text-red-500 transition-colors p-1"
          onClick={onClose}
        >
          <FaTimes size={20} />
        </button>

        <div className="text-center">
          {status === "loading" && (
            <>
              <FaSpinner className="text-blue-500 text-5xl mx-auto mb-4 animate-spin" />
              <p className="text-gray-700 font-medium text-lg">Saving your profile...</p>
              <p className="text-gray-500 text-sm mt-2">Please wait while we update your information</p>
            </>
          )}

          {status === "success" && (
            <>
              <FaCheckCircle className="text-green-500 text-5xl mx-auto mb-4" />
              <p className="text-green-700 font-bold text-xl mb-2">Success!</p>
              <p className="text-gray-600">Your profile has been saved successfully</p>
            </>
          )}

          {status === "error" && (
            <>
              <FaTimesCircle className="text-red-500 text-5xl mx-auto mb-4" />
              <p className="text-red-700 font-bold text-xl mb-2">Error!</p>
              <p className="text-gray-600">Failed to save profile. Please check the errors and try again.</p>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

const Profile = () => {
  const {
    company,
    getCompanyProfile,
    updateCompanyProfile,
    loading,
    error,
  } = useCompanyStore();

  const [form, setForm] = useState(initialState);
  const [logoPreview, setLogoPreview] = useState(null);
  const [verificationPreview, setVerificationPreview] = useState(null);
  const [saveStatus, setSaveStatus] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [backendErrors, setBackendErrors] = useState([]);
  const [hasDataLoaded, setHasDataLoaded] = useState(false);

  // Industry options
  const industryOptions = useMemo(() =>
    INDUSTRY_OPTIONS.map(industry => ({ value: industry, label: industry })),
    []
  );

  // Company size options
  const sizeOptions = useMemo(() => COMPANY_SIZE_OPTIONS, []);

  // Load profile data only once
  useEffect(() => {
    if (hasDataLoaded) return; // Prevent multiple calls

    const fetchProfile = async () => {
      try {
        await getCompanyProfile();
        setHasDataLoaded(true);
      } catch (err) {
        console.error("Error fetching profile:", err);
      }
    };

    fetchProfile();
  }, [hasDataLoaded, getCompanyProfile]);

  // Update form when company data changes
  useEffect(() => {
    if (company && hasDataLoaded) {
      setForm({
        companyName: company.companyName || "",
        companyEmail: company.companyEmail || "",
        website: company.website || "",
        description: company.description || "",
        industry: company.industry || "",
        companySize: company.companySize || "",
        logo: null,
        address: company.location?.address || "",
        city: company.location?.city || "",
        state: company.location?.state || "",
        country: company.location?.country || "",
        pincode: company.location?.pincode || "",
        contactPersonName: company.contactPerson?.name || "",
        contactPersonDesignation: company.contactPerson?.designation || "",
        contactPersonPhone: company.contactPerson?.phone || "",
        linkedin: company.socialLinks?.linkedin || "",
        twitter: company.socialLinks?.twitter || "",
        facebook: company.socialLinks?.facebook || "",
        github: company.socialLinks?.github || "",
        verificationDocument: null,
      });
      setLogoPreview(company.logo || null);
      setVerificationPreview(company.verificationDocument || null);
    }
  }, [company, hasDataLoaded]);

  const handleChange = useCallback((e) => {
    const { name, type, value, files } = e.target;

    if (type === "file") {
      const file = files[0];
      if (file) {
        setForm((prev) => ({ ...prev, [name]: file }));
        const objectUrl = URL.createObjectURL(file);

        if (name === "logo") {
          setLogoPreview(objectUrl);
        } else if (name === "verificationDocument") {
          setVerificationPreview(objectUrl);
        }
      }
    } else {
      setForm((prev) => ({ ...prev, [name]: value }));
    }
  }, []);

  const validateForm = () => {
    // Use the comprehensive validation utility
    const validation = validateCompanyForm(form);

    // Auto-fix website URL if needed
    if (form.website && form.website.trim() && !form.website.startsWith('http')) {
      setForm(prev => ({ ...prev, website: `https://${prev.website}` }));
    }

    return validation.errors;
  };

  const handleSubmit = async (e) => {
    if (e && e.preventDefault) {
      e.preventDefault();
    }

    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setBackendErrors(validationErrors);
      setSaveStatus("error");
      setShowModal(true);
      return;
    }

    setSaveStatus("loading");
    setBackendErrors([]);
    setShowModal(true);

    const payload = {
      companyName: form.companyName.trim(),
      companyEmail: form.companyEmail.trim().toLowerCase(),
      website: form.website,
      description: form.description,
      industry: form.industry,
      companySize: form.companySize,
      logo: form.logo,
      location: {
        address: form.address,
        city: form.city,
        state: form.state,
        country: form.country,
        pincode: form.pincode,
      },
      contactPerson: {
        name: form.contactPersonName,
        designation: form.contactPersonDesignation,
        phone: form.contactPersonPhone,
      },
      socialLinks: {
        linkedin: form.linkedin,
        twitter: form.twitter,
        facebook: form.facebook,
        github: form.github,
      },
      verificationDocument: form.verificationDocument,
    };

    try {
      const hasFile = form.logo instanceof File || form.verificationDocument instanceof File;

      let result;
      if (hasFile) {
        const formData = new FormData();
        Object.entries(payload).forEach(([key, val]) => {
          if (["location", "contactPerson", "socialLinks"].includes(key)) {
            formData.append(key, JSON.stringify(val));
          } else if (val !== null && val !== undefined) {
            formData.append(key, val);
          }
        });
        result = await updateCompanyProfile(formData);
      } else {
        result = await updateCompanyProfile(payload);
      }

      if (result?.error) {
        setBackendErrors(result.details || [result.error]);
        setSaveStatus("error");
      } else {
        setSaveStatus("success");
        setBackendErrors([]);
        // Auto-close modal after success
        setTimeout(() => {
          setShowModal(false);
          setSaveStatus(null);
        }, 2000);
      }
    } catch (err) {
      console.error("Submit error:", err);
      setBackendErrors(["An unexpected error occurred. Please try again."]);
      setSaveStatus("error");
    }
  };

  if (loading && !hasDataLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FaSpinner className="text-blue-600 text-6xl mx-auto mb-4 animate-spin" />
          <p className="text-xl text-gray-700 font-medium">Loading company profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-[rgb(35,65,75)] to-gray-700 rounded-lg flex items-center justify-center">
              <FaBuilding className="text-white text-xl" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Company Profile</h1>
              <p className="text-sm text-gray-600">Manage your company information and settings</p>
            </div>
          </div>
          {/* Company Logo Display */}
          {logoPreview && (
            <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
              <img
                src={logoPreview}
                alt="Company Logo"
                className="h-12 w-12 object-contain rounded"
              />
            </div>
          )}
        </div>

        {/* Company Summary */}
        {company && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">{company.companyName}</h2>
                <p className="text-sm text-gray-600 mt-1">{company.industry} • {company.companySize} employees</p>
              </div>
              <div className="text-left md:text-right">
                <p className="text-sm text-gray-600">{company.location?.city}, {company.location?.country}</p>
                <a href={company.website} target="_blank" rel="noopener noreferrer" className="text-sm text-[rgb(35,65,75)] hover:underline">
                  {company.website}
                </a>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">

        <div className="p-8">
          {/* Error Display */}
          {backendErrors.length > 0 && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded-lg">
              <div className="flex">
                <FaTimesCircle className="text-red-500 mt-1 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Please fix the following errors:</h3>
                  <ul className="text-red-700 text-sm mt-2 list-disc list-inside">
                    {backendErrors.map((err, idx) => (
                      <li key={idx}>{err}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-8">
            {/* Basic Information */}
            <section>
              <SectionHeader title="Basic Information" icon={FaBuilding} />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <InputField
                  label="Company Name"
                  name="companyName"
                  value={form.companyName}
                  onChange={handleChange}
                  required
                  placeholder="Enter your company name"
                />
                <InputField
                  label="Company Email"
                  name="companyEmail"
                  value={form.companyEmail}
                  onChange={handleChange}
                  required
                  type="email"
                  placeholder="<EMAIL>"
                />
                <InputField
                  label="Website"
                  name="website"
                  value={form.website}
                  onChange={handleChange}
                  placeholder="https://www.company.com"
                />
                <SelectField
                  label="Industry"
                  name="industry"
                  value={form.industry}
                  onChange={handleChange}
                  options={industryOptions}
                />
                <div className="md:col-span-2">
                  <SelectField
                    label="Company Size"
                    name="companySize"
                    value={form.companySize}
                    onChange={handleChange}
                    options={sizeOptions}
                  />
                </div>
              </div>

              <div className="mt-6">
                <TextAreaField
                  label="Company Description"
                  name="description"
                  value={form.description}
                  onChange={handleChange}
                  placeholder="Tell us about your company..."
                  rows={4}
                />
              </div>
            </section>

            {/* Location Information */}
            <section>
              <SectionHeader title="Location" icon={FaMapMarkerAlt} />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="md:col-span-2 lg:col-span-3">
                  <InputField
                    label="Address"
                    name="address"
                    value={form.address}
                    onChange={handleChange}
                    required
                    placeholder="Street address"
                  />
                </div>
                <InputField
                  label="City"
                  name="city"
                  value={form.city}
                  onChange={handleChange}
                  required
                  placeholder="City"
                />
                <InputField
                  label="State"
                  name="state"
                  value={form.state}
                  onChange={handleChange}
                  required
                  placeholder="State"
                />
                <InputField
                  label="Country"
                  name="country"
                  value={form.country}
                  onChange={handleChange}
                  required
                  placeholder="Country"
                />
                <InputField
                  label="Postal Code"
                  name="pincode"
                  value={form.pincode}
                  onChange={handleChange}
                  required
                  placeholder="Postal code"
                />
              </div>
            </section>

            {/* Contact Person */}
            <section>
              <SectionHeader title="Contact Person" icon={FaUser} />
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <InputField
                  label="Full Name"
                  name="contactPersonName"
                  value={form.contactPersonName}
                  onChange={handleChange}
                  required
                  placeholder="Contact person name"
                />
                <InputField
                  label="Designation"
                  name="contactPersonDesignation"
                  value={form.contactPersonDesignation}
                  onChange={handleChange}
                  required
                  placeholder="Job title"
                />
                <InputField
                  label="Phone Number"
                  name="contactPersonPhone"
                  value={form.contactPersonPhone}
                  onChange={handleChange}
                  required
                  type="tel"
                  placeholder="******-0123"
                />
              </div>
            </section>

            {/* Social Links */}
            <section>
              <SectionHeader title="Social Media Links" icon={FaShare} />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <InputField
                  label="LinkedIn"
                  name="linkedin"
                  value={form.linkedin}
                  onChange={handleChange}
                  placeholder="https://linkedin.com/company/yourcompany"
                />
                <InputField
                  label="Twitter"
                  name="twitter"
                  value={form.twitter}
                  onChange={handleChange}
                  placeholder="https://twitter.com/yourcompany"
                />
                <InputField
                  label="Facebook"
                  name="facebook"
                  value={form.facebook}
                  onChange={handleChange}
                  placeholder="https://facebook.com/yourcompany"
                />
                <InputField
                  label="GitHub"
                  name="github"
                  value={form.github}
                  onChange={handleChange}
                  placeholder="https://github.com/yourcompany"
                />
              </div>
            </section>

            {/* File Uploads */}
            <section>
              <SectionHeader title="Documents & Media" icon={FaImage} />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <FileUpload
                  label="Company Logo"
                  name="logo"
                  onChange={handleChange}
                  accept="image/*"
                  preview={logoPreview}
                  icon={FaImage}
                />
                <FileUpload
                  label="Verification Document"
                  name="verificationDocument"
                  onChange={handleChange}
                  accept=".pdf,image/*"
                  preview={verificationPreview}
                  icon={FaFileAlt}
                />
              </div>
            </section>

            {/* Submit Button */}
            <div className="flex justify-end pt-6 border-t border-gray-200">
              <button
                onClick={handleSubmit}
                disabled={loading}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-8 rounded-lg flex items-center gap-3 transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-lg"
              >
                {loading ? (
                  <FaSpinner className="animate-spin" />
                ) : (
                  <FaSave />
                )}
                {loading ? "Saving..." : "Save Profile"}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Status Modal */}
      <StatusModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        status={saveStatus}
      />
    </div>
  );
};

export default Profile;