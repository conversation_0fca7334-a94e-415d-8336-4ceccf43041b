
/**
 * *Validate company profile data
 * *@param {Object} data - Company profile data
 * *@returns {Object} - { isValid: boolean, errors: Array }
 */
export const validateCompanyProfile = (data) => {
    const errors = [];

    // Company name validation
    if (!data.companyName || typeof data.companyName !== 'string' || !data.companyName.trim()) {
        errors.push('Company name is required and must be a non-empty string.');
    } else if (data.companyName.trim().length < 2) {
        errors.push('Company name must be at least 2 characters long.');
    } else if (data.companyName.length > 100) {
        errors.push('Company name must not exceed 100 characters.');
    }

    // Email validation
    if (!data.companyEmail || typeof data.companyEmail !== 'string' || !data.companyEmail.trim()) {
        errors.push('Valid company email is required.');
    } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.companyEmail.trim())) {
            errors.push('Please enter a valid email address.');
        }
    }

    // Website validation (optional)
    if (data.website && data.website.trim()) {
        const urlRegex = /^https?:\/\/.+/;
        if (!urlRegex.test(data.website)) {
            errors.push('Website must be a valid URL starting with http:// or https://');
        }
    }

    // Industry validation (optional but recommended)
    if (data.industry && data.industry.length > 50) {
        errors.push('Industry must not exceed 50 characters.');
    }

    // Company size validation (optional)
    const validSizes = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'];
    if (data.companySize && !validSizes.includes(data.companySize)) {
        errors.push('Please select a valid company size.');
    }

    // Description validation (optional)
    if (data.description && data.description.length > 2000) {
        errors.push('Company description must not exceed 2000 characters.');
    }

    // Location validation
    if (data.location && typeof data.location === 'object') {
        const { address, city, state, country, pincode } = data.location;

        if (!address || !address.trim()) {
            errors.push('Address is required.');
        } else if (address.length > 200) {
            errors.push('Address must not exceed 200 characters.');
        }

        if (!city || !city.trim()) {
            errors.push('City is required.');
        } else if (city.length > 50) {
            errors.push('City must not exceed 50 characters.');
        }

        if (!state || !state.trim()) {
            errors.push('State is required.');
        } else if (state.length > 50) {
            errors.push('State must not exceed 50 characters.');
        }

        if (!country || !country.trim()) {
            errors.push('Country is required.');
        } else if (country.length > 50) {
            errors.push('Country must not exceed 50 characters.');
        }

        if (!pincode || !pincode.trim()) {
            errors.push('Pincode is required.');
        } else if (!/^\d{5,10}$/.test(pincode.trim())) {
            errors.push('Pincode must be 5-10 digits.');
        }
    } else {
        errors.push('Location information is required.');
    }

    // Contact person validation
    if (data.contactPerson && typeof data.contactPerson === 'object') {
        const { name, designation, phone } = data.contactPerson;

        if (!name || !name.trim()) {
            errors.push('Contact person name is required.');
        } else if (name.length > 100) {
            errors.push('Contact person name must not exceed 100 characters.');
        }

        if (!designation || !designation.trim()) {
            errors.push('Contact person designation is required.');
        } else if (designation.length > 100) {
            errors.push('Contact person designation must not exceed 100 characters.');
        }

        if (!phone || !phone.trim()) {
            errors.push('Contact person phone is required.');
        } else if (!/^[\+]?[1-9][\d]{0,15}$/.test(phone.replace(/[\s\-\(\)]/g, ''))) {
            errors.push('Please enter a valid phone number.');
        }
    } else {
        errors.push('Contact person information is required.');
    }

    // Social links validation (optional)
    if (data.socialLinks && typeof data.socialLinks === 'object') {
        const { linkedin, twitter, facebook, github } = data.socialLinks;

        if (linkedin && linkedin.trim() && !isValidUrl(linkedin)) {
            errors.push('LinkedIn URL is not valid.');
        }

        if (twitter && twitter.trim() && !isValidUrl(twitter)) {
            errors.push('Twitter URL is not valid.');
        }

        if (facebook && facebook.trim() && !isValidUrl(facebook)) {
            errors.push('Facebook URL is not valid.');
        }

        if (github && github.trim() && !isValidUrl(github)) {
            errors.push('GitHub URL is not valid.');
        }
    }

    return { isValid: errors.length === 0, errors };
};

/**
 * Validate URL format
 * @param {string} url - URL to validate
 * @returns {boolean} - Whether URL is valid
 */
const isValidUrl = (url) => {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
};

/**
 * Validate form data before submission
 * @param {Object} formData - Form data object
 * @returns {Object} - { isValid: boolean, errors: Array }
 */
export const validateCompanyForm = (formData) => {
    // Transform form data to match API structure
    const apiData = {
        companyName: formData.companyName,
        companyEmail: formData.companyEmail,
        website: formData.website,
        industry: formData.industry,
        companySize: formData.companySize,
        description: formData.description,
        location: {
            address: formData.address,
            city: formData.city,
            state: formData.state,
            country: formData.country,
            pincode: formData.pincode,
        },
        contactPerson: {
            name: formData.contactPersonName,
            designation: formData.contactPersonDesignation,
            phone: formData.contactPersonPhone,
        },
        socialLinks: {
            linkedin: formData.linkedin,
            twitter: formData.twitter,
            facebook: formData.facebook,
            github: formData.github,
        }
    };

    return validateCompanyProfile(apiData);
};

/**
 ** Get field-specific validation errors
 */
export const validateField = (fieldName, value, allData = {}) => {
    switch (fieldName) {
        case 'companyName':
            if (!value || !value.trim()) return 'Company name is required';
            if (value.trim().length < 2) return 'Company name must be at least 2 characters';
            if (value.length > 100) return 'Company name must not exceed 100 characters';
            break;

        case 'companyEmail':
            if (!value || !value.trim()) return 'Company email is required';
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value.trim())) return 'Please enter a valid email address';
            break;

        case 'website':
            if (value && value.trim() && !/^https?:\/\/.+/.test(value)) {
                return 'Website must start with http:// or https://';
            }
            break;

        case 'contactPersonPhone':
            if (!value || !value.trim()) return 'Phone number is required';
            if (!/^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, ''))) {
                return 'Please enter a valid phone number';
            }
            break;

        case 'pincode':
            if (!value || !value.trim()) return 'Pincode is required';
            if (!/^\d{5,10}$/.test(value.trim())) return 'Pincode must be 5-10 digits';
            break;

        default:
            // For required text fields
            if (['address', 'city', 'state', 'country', 'contactPersonName', 'contactPersonDesignation'].includes(fieldName)) {
                if (!value || !value.trim()) {
                    const fieldLabel = fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                    return `${fieldLabel} is required`;
                }
            }
            break;
    }

    return null;
};

/**
 * Industry options for dropdown
 */
export const INDUSTRY_OPTIONS = [
    'Technology',
    'Healthcare',
    'Finance',
    'Education',
    'Manufacturing',
    'Retail',
    'Consulting',
    'Media & Entertainment',
    'Real Estate',
    'Transportation',
    'Energy',
    'Government',
    'Non-profit',
    'Other'
];

/**
 * Company size options for dropdown
 */



export const COMPANY_SIZE_OPTIONS = [
    { value: '1-10', label: '1-10 employees' },
    { value: '11-50', label: '11-50 employees' },
    { value: '51-200', label: '51-200 employees' },
    { value: '201-500', label: '201-500 employees' },
    { value: '501-1000', label: '501-1000 employees' },
    { value: '1000+', label: '1000+ employees' }
];
