import React from 'react';

const TestCard = ({ name, date, onViewDetails }) => (
  <div className="bg-white rounded-xl shadow p-6 flex flex-col">
    <span className="font-bold text-lg mb-2">{name}</span>
    <span className="text-gray-500 mb-4">{date}</span>
    <button
      className="mt-auto bg-blue-600 text-white rounded px-4 py-2 font-semibold hover:bg-blue-700 transition"
      onClick={onViewDetails}
    >
      View Details
    </button>
  </div>
);

export default TestCard; 