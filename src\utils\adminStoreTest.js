// Test utility to verify admin store caching and deduplication
import useAdminStore from '../store/adminStore';

export const testAdminStoreOptimization = async () => {
    console.log('🧪 Testing Admin Store Optimization...');
    
    const store = useAdminStore.getState();
    
    // Test 1: Multiple simultaneous calls should be deduplicated
    console.log('Test 1: Request deduplication');
    const startTime = Date.now();
    
    const promises = [
        store.getUsers({ limit: 10 }),
        store.getUsers({ limit: 10 }),
        store.getUsers({ limit: 10 }),
        store.getJobs({ limit: 10 }),
        store.getJobs({ limit: 10 }),
        store.getTests({ limit: 10 }),
        store.getTests({ limit: 10 })
    ];
    
    const results = await Promise.allSettled(promises);
    const endTime = Date.now();
    
    console.log(`✅ Completed ${promises.length} requests in ${endTime - startTime}ms`);
    console.log('Results:', results.map(r => r.status === 'fulfilled' ? 
        (r.value.fromCache ? 'CACHED' : 'FRESH') : 'ERROR'));
    
    // Test 2: Cache validation
    console.log('\nTest 2: Cache validation');
    const cacheState = store.cache;
    console.log('Cache timestamps:', {
        users: cacheState.users.timestamp ? new Date(cacheState.users.timestamp).toISOString() : 'null',
        jobs: cacheState.jobs.timestamp ? new Date(cacheState.jobs.timestamp).toISOString() : 'null',
        tests: cacheState.tests.timestamp ? new Date(cacheState.tests.timestamp).toISOString() : 'null'
    });
    
    // Test 3: Subsequent calls should use cache
    console.log('\nTest 3: Cache usage');
    const cachedStartTime = Date.now();
    const cachedResults = await Promise.allSettled([
        store.getUsers({ limit: 10 }),
        store.getJobs({ limit: 10 }),
        store.getTests({ limit: 10 })
    ]);
    const cachedEndTime = Date.now();
    
    console.log(`✅ Cached requests completed in ${cachedEndTime - cachedStartTime}ms`);
    console.log('Cached results:', cachedResults.map(r => r.status === 'fulfilled' ? 
        (r.value.fromCache ? 'CACHED ✅' : 'FRESH ⚠️') : 'ERROR ❌'));
    
    // Test 4: Loading state management
    console.log('\nTest 4: Loading state management');
    console.log('Loading states:', store.loadingStates);
    
    return {
        deduplicationTime: endTime - startTime,
        cacheTime: cachedEndTime - cachedStartTime,
        cacheHits: cachedResults.filter(r => r.status === 'fulfilled' && r.value.fromCache).length,
        totalRequests: promises.length + cachedResults.length
    };
};

// Helper to monitor API calls in development
export const monitorApiCalls = () => {
    if (process.env.NODE_ENV !== 'development') return;
    
    const originalFetch = window.fetch;
    const apiCalls = new Map();
    
    window.fetch = async (...args) => {
        const url = args[0];
        const key = typeof url === 'string' ? url : url.url;
        
        if (key.includes('/api/admin/')) {
            const count = apiCalls.get(key) || 0;
            apiCalls.set(key, count + 1);
            
            console.log(`🌐 API Call #${count + 1}: ${key}`);
            
            if (count > 0) {
                console.warn(`⚠️ Duplicate API call detected: ${key} (called ${count + 1} times)`);
            }
        }
        
        return originalFetch(...args);
    };
    
    // Log summary every 10 seconds
    setInterval(() => {
        if (apiCalls.size > 0) {
            console.log('📊 API Call Summary:', Object.fromEntries(apiCalls));
        }
    }, 10000);
};

// Auto-start monitoring in development
if (process.env.NODE_ENV === 'development') {
    monitorApiCalls();
}
