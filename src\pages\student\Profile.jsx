import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useStudentStore from '../../store/studentStore';
import { FaUserEdit, FaFileAlt, FaMedal, FaBriefcase, FaGraduationCap, FaTasks, FaCertificate, FaProjectDiagram, FaTrophy, FaEdit, FaShareAlt, FaLinkedin, FaGithub, FaTwitter, FaInstagram, FaFacebook, FaGlobe, FaEnvelope, FaStar, FaCamera, FaSpinner, FaExclamationTriangle } from 'react-icons/fa';

// Static profile data - replaced with dynamic data from API
const initialProfile = {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  email: 'gabbisheik5297',
  address: 'Alva’s Institute of Engineering and Technology, Karnataka',
  avatar: '',
  about: '',
  resume: '',
  skills: [],
  work: [],
  education: [],
  responsibilities: [],
  certificates: [],
  projects: [],
  achievements: [],
  social: [
    { icon: <FaLinkedin />, url: '#' },
    { icon: <FaGithub />, url: '#' },
    { icon: <FaTwitter />, url: '#' },
    { icon: <FaInstagram />, url: '#' },
    { icon: <FaFacebook />, url: '#' },
    { icon: <FaGlobe />, url: '#' },
    { icon: <FaEnvelope />, url: '#' },
  ],
  streaks: [
    '2024-06-01', '2024-06-02', '2024-06-03', '2024-06-05', '2024-06-07',
    '2024-06-10', '2024-06-12', '2024-06-13', '2024-06-14', '2024-06-15',
  ],
};

const sectionKeys = [
  'about', 'resume', 'skills', 'work', 'education', 'responsibilities', 'certificates', 'projects', 'achievements'
];

const Profile = () => {
  const navigate = useNavigate();
  const {
    profile,
    profileLoading,
    profileError,
    updateProfile,
    saveProfile,
    fetchProfile,
    getProfileCompletion
  } = useStudentStore();

  const [editSection, setEditSection] = useState(null);
  const [temp, setTemp] = useState({});
  const [editHeader, setEditHeader] = useState(false);
  const [headerTemp, setHeaderTemp] = useState({
    name: profile.name,
    email: profile.email,
    address: profile.address,
    avatar: profile.avatar
  });
  const [dragActive, setDragActive] = useState(false);
  const [showSaved, setShowSaved] = useState(false);
  const fileInputRef = useRef();
  const [showProfileModal, setShowProfileModal] = useState(false);

  // Fetch profile on component mount and set up auto-refresh
  useEffect(() => {
    fetchProfile();

    // Auto-refresh profile every 5 minutes to keep data current
    const interval = setInterval(() => {
      fetchProfile();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [fetchProfile]);

  // Update headerTemp when profile changes
  useEffect(() => {
    setHeaderTemp({
      name: profile.name,
      email: profile.email,
      address: profile.address,
      avatar: profile.avatar
    });
  }, [profile]);

  // Use completion status from API, fallback to calculated completion
  const progress = profile.completionStatus || getProfileCompletion();

  // Handlers
  const handleEdit = (key) => {
    setEditSection(key);
    setTemp({ ...profile });
  };
  const handleCancel = () => setEditSection(null);
  const handleSave = async (key) => {
    try {
      updateProfile({ [key]: temp[key] });
      await saveProfile({ [key]: temp[key] });
      setEditSection(null);
      // Refresh profile data to ensure we have the latest from server
      await fetchProfile();
    } catch (error) {
      console.error('Failed to save profile:', error);
    }
  };
  const handleTempChange = (key, value) => setTemp({ ...temp, [key]: value });

  // Header edit handlers
  const handleHeaderEdit = () => {
    setHeaderTemp({ name: profile.name, email: profile.email, address: profile.address, avatar: profile.avatar });
    setEditHeader(true);
  };
  const handleHeaderCancel = () => setEditHeader(false);
  const handleHeaderSave = async () => {
    try {
      updateProfile(headerTemp);
      await saveProfile(headerTemp);
      setEditHeader(false);
      setShowSaved(true);
      setTimeout(() => setShowSaved(false), 1500);
      // Refresh profile data to ensure we have the latest from server
      await fetchProfile();
    } catch (error) {
      console.error('Failed to save profile header:', error);
    }
  };

  // Avatar upload handlers (update headerTemp.avatar if editing)
  const handleAvatarChange = (e) => {
    const file = e.target.files && e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (ev) => {
        if (editHeader) {
          setHeaderTemp((prev) => ({ ...prev, avatar: ev.target.result }));
        } else {
          updateProfile({ avatar: ev.target.result });
        }
      };
      reader.readAsDataURL(file);
    }
  };
  const handleAvatarDrop = (e) => {
    e.preventDefault();
    setDragActive(false);
    const file = e.dataTransfer.files && e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (ev) => {
        if (editHeader) {
          setHeaderTemp((prev) => ({ ...prev, avatar: ev.target.result }));
        } else {
          updateProfile({ avatar: ev.target.result });
        }
      };
      reader.readAsDataURL(file);
    }
  };
  const handleAvatarDragOver = (e) => {
    e.preventDefault();
    setDragActive(true);
  };
  const handleAvatarDragLeave = (e) => {
    e.preventDefault();
    setDragActive(false);
  };
  const handleAvatarClick = () => {
    fileInputRef.current.click();
  };

  // Loading state
  if (profileLoading) {
    return (
      <div className="min-h-screen bg-gray-50 w-full flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Profile...</h2>
          <p className="text-gray-500">Please wait while we fetch your profile data.</p>
        </div>
      </div>
    );
  }

  // Error state
  if (profileError) {
    return (
      <div className="min-h-screen bg-gray-50 w-full flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <FaExclamationTriangle className="text-4xl text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Profile</h2>
          <p className="text-gray-500 mb-4">{profileError}</p>
          <button
            onClick={() => fetchProfile()}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Navigation Bar */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <button
              className="flex items-center gap-2 text-[#23414c] font-semibold px-3 py-2 rounded-lg hover:bg-[#23414c]/10 transition-all duration-200"
              onClick={() => navigate(-1)}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
              </svg>
              Back to Dashboard
            </button>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">Profile Completion</div>
              <div className="flex items-center gap-2">
                <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-2 bg-gradient-to-r from-[#23414c] to-blue-600 rounded-full transition-all duration-500"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <span className="text-sm font-semibold text-[#23414c]">{progress}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Header Section */}
      <div className="relative bg-gradient-to-r from-[#23414c] via-[#2a4a57] to-[#23414c] overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8">
            {/* Avatar Section */}
            <div className="flex-shrink-0 relative group">
              <div
                className={`w-32 h-32 rounded-full flex items-center justify-center text-6xl font-bold border-4 border-white shadow-2xl cursor-pointer transition-all duration-300 group-hover:scale-105 ${dragActive ? 'ring-4 ring-white/50 scale-105' : ''}`}
                style={{
                  background: (editHeader ? headerTemp.avatar : profile.avatar)
                    ? `url(${editHeader ? headerTemp.avatar : profile.avatar}) center/cover no-repeat`
                    : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                }}
                onClick={handleAvatarClick}
                onDrop={handleAvatarDrop}
                onDragOver={handleAvatarDragOver}
                onDragLeave={handleAvatarDragLeave}
                title="Click or drag to upload photo"
              >
                {!(editHeader ? headerTemp.avatar : profile.avatar) && (
                  <span className="text-white drop-shadow-lg">
                    {profile.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </span>
                )}
                <div className="absolute inset-0 bg-black/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <FaCamera className="text-white text-2xl" />
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleAvatarChange}
                />
              </div>
              {dragActive && (
                <div className="absolute inset-0 rounded-full border-4 border-dashed border-white/70 flex items-center justify-center bg-white/10">
                  <span className="text-white font-semibold">Drop photo here</span>
                </div>
              )}
            </div>

            {/* Profile Info */}
            <div className="flex-1 text-center lg:text-left">
              {editHeader ? (
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-4">
                  <input
                    className="w-full rounded-lg px-4 py-3 text-xl font-bold bg-white/90 text-gray-800 placeholder-gray-500 border-0 focus:ring-2 focus:ring-white"
                    value={headerTemp.name}
                    onChange={e => setHeaderTemp({ ...headerTemp, name: e.target.value })}
                    placeholder="Full Name"
                  />
                  <input
                    className="w-full rounded-lg px-4 py-3 text-base bg-white/90 text-gray-800 placeholder-gray-500 border-0 focus:ring-2 focus:ring-white"
                    value={headerTemp.email}
                    onChange={e => setHeaderTemp({ ...headerTemp, email: e.target.value })}
                    placeholder="Email Address"
                  />
                  <input
                    className="w-full rounded-lg px-4 py-3 text-base bg-white/90 text-gray-800 placeholder-gray-500 border-0 focus:ring-2 focus:ring-white"
                    value={headerTemp.address}
                    onChange={e => setHeaderTemp({ ...headerTemp, address: e.target.value })}
                    placeholder="Location"
                  />
                  <div className="flex gap-3 pt-2">
                    <button
                      className="bg-white text-[#23414c] px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex-1"
                      onClick={handleHeaderSave}
                      disabled={JSON.stringify(headerTemp) === JSON.stringify({ name: profile.name, email: profile.email, address: profile.address, avatar: profile.avatar })}
                    >
                      Save Changes
                    </button>
                    <button
                      className="bg-white/20 text-white px-6 py-2 rounded-lg font-semibold hover:bg-white/30 transition-colors"
                      onClick={handleHeaderCancel}
                    >
                      Cancel
                    </button>
                  </div>
                  {showSaved && (
                    <div className="bg-green-500 text-white px-4 py-2 rounded-lg text-center font-semibold">
                      Profile Updated Successfully!
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <h1 className="text-4xl lg:text-5xl font-bold text-white mb-2 leading-tight">
                      {profile.name}
                    </h1>
                    <p className="text-xl text-white/90 mb-1">{profile.email}</p>
                    <p className="text-lg text-white/80 flex items-center justify-center lg:justify-start gap-2">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                      {profile.address}
                    </p>
                  </div>

                  {/* Quick Stats */}
                  <div className="flex flex-wrap gap-4 justify-center lg:justify-start pt-4">
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                      <div className="text-2xl font-bold text-white">{profile.skills?.length || 0}</div>
                      <div className="text-sm text-white/80">Skills</div>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                      <div className="text-2xl font-bold text-white">{profile.projects?.length || 0}</div>
                      <div className="text-sm text-white/80">Projects</div>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                      <div className="text-2xl font-bold text-white">{profile.certificates?.length || 0}</div>
                      <div className="text-sm text-white/80">Certificates</div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Action Button */}
            <div className="flex-shrink-0">
              <button
                className="bg-white text-[#23414c] font-bold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 flex items-center gap-3 text-lg"
                onClick={handleHeaderEdit}
                disabled={editHeader}
              >
                <FaUserEdit className="text-xl" />
                {editHeader ? 'Editing...' : 'Edit Profile'}
              </button>
            </div>
          </div>
        </div>
      </div>
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col xl:flex-row gap-8">
          {/* Main Content Area */}
          <div className="flex-1 space-y-6">
          {/* About Section */}
          <ProfileSection
            icon={<FaUserEdit />}
            title="About Me"
            actionLabel={profile.about ? 'Edit About' : 'Add About'}
            onEdit={() => handleEdit('about')}
            isEditing={editSection === 'about'}
          >
            {editSection === 'about' ? (
              <div className="space-y-4">
                <textarea
                  className="w-full border border-gray-300 rounded-lg p-4 text-gray-700 placeholder-gray-400 focus:ring-2 focus:ring-[#23414c]/20 focus:border-[#23414c] transition-colors resize-none"
                  rows={4}
                  value={temp.about || ''}
                  onChange={e => handleTempChange('about', e.target.value)}
                  placeholder="Tell us about yourself, your interests, and career goals..."
                />
                <div className="flex gap-3">
                  <button
                    className="bg-[#23414c] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#23414c]/90 transition-colors"
                    onClick={() => handleSave('about')}
                  >
                    Save Changes
                  </button>
                  <button
                    className="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg font-semibold hover:bg-gray-200 transition-colors"
                    onClick={handleCancel}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : profile.about ? (
              <div className="text-gray-700 leading-relaxed whitespace-pre-line bg-gray-50 p-4 rounded-lg">
                {profile.about}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-400 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                <FaUserEdit className="mx-auto text-3xl mb-2 text-gray-300" />
                <p>Share something about yourself to help others get to know you better.</p>
              </div>
            )}
          </ProfileSection>
          {/* Resume Section */}
          <ProfileSection
            icon={<FaFileAlt />} title="Resume"
            actionLabel={profile.resume ? 'Edit Resume' : 'Upload Resume'}
            isEditing={editSection === 'resume'}
            onEdit={() => handleEdit('resume')}
          >
            {editSection === 'resume' ? (
              <div className="flex flex-col gap-2">
                <input
                  type="text"
                  className="border rounded p-2 w-full"
                  placeholder="Paste resume link or description"
                  value={temp.resume || ''}
                  onChange={e => handleTempChange('resume', e.target.value)}
                />
                <input
                  type="file"
                  accept=".pdf,.doc,.docx,.txt"
                  className="border rounded p-2 w-full"
                  onChange={e => {
                    const file = e.target.files[0];
                    if (file) {
                      const reader = new FileReader();
                      reader.onload = (ev) => {
                        setTemp({ ...temp, resumeFile: { name: file.name, data: ev.target.result } });
                      };
                      reader.readAsDataURL(file);
                    }
                  }}
                />
                {temp.resumeFile && (
                  <div className="text-green-700 text-sm">Selected: {temp.resumeFile.name}</div>
                )}
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={async () => {
                    try {
                      const updates = temp.resumeFile
                        ? { resume: temp.resume, resumeFile: temp.resumeFile }
                        : { resume: temp.resume };
                      updateProfile(updates);
                      await saveProfile(updates);
                      setEditSection(null);
                    } catch (error) {
                      console.error('Failed to save resume:', error);
                    }
                  }}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.resumeFile ? (
              <div className="flex flex-col gap-2">
                <a
                  href={profile.resumeFile.data}
                  download={profile.resumeFile.name}
                  className="text-blue-700 underline font-semibold"
                >
                  Download Resume: {profile.resumeFile.name}
                </a>
                {profile.resume && <div className="text-gray-700 break-all">{profile.resume}</div>}
              </div>
            ) : profile.resume ? (
              <div className="text-gray-700 break-all">{profile.resume}</div>
            ) : (
              <div className="text-gray-400">No resume uploaded yet.</div>
            )}
          </ProfileSection>
          {/* Skills Section */}
          <ProfileSection
            icon={<FaStar />}
            title="Skills & Expertise"
            actionLabel={profile.skills?.length ? 'Edit Skills' : 'Add Skills'}
            onEdit={() => handleEdit('skills')}
            isEditing={editSection === 'skills'}
          >
            {editSection === 'skills' ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Add your skills (separate with commas)
                  </label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-lg p-4 text-gray-700 placeholder-gray-400 focus:ring-2 focus:ring-[#23414c]/20 focus:border-[#23414c] transition-colors"
                    placeholder="e.g., JavaScript, React, Node.js, Python, Machine Learning"
                    value={temp.skills ? temp.skills.join(', ') : ''}
                    onChange={e => handleTempChange('skills', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                  />
                </div>
                {temp.skills?.length > 0 && (
                  <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg">
                    {temp.skills.map((skill, idx) => (
                      <span key={idx} className="bg-[#23414c] text-white px-3 py-1 rounded-full text-sm font-medium">
                        {skill}
                      </span>
                    ))}
                  </div>
                )}
                <div className="flex gap-3">
                  <button
                    className="bg-[#23414c] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#23414c]/90 transition-colors"
                    onClick={() => handleSave('skills')}
                  >
                    Save Skills
                  </button>
                  <button
                    className="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg font-semibold hover:bg-gray-200 transition-colors"
                    onClick={handleCancel}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : profile.skills?.length ? (
              <div className="flex flex-wrap gap-3">
                {profile.skills.map((skill, idx) => (
                  <span
                    key={idx}
                    className="bg-gradient-to-r from-[#23414c] to-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-sm hover:shadow-md transition-shadow"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-400 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                <FaStar className="mx-auto text-3xl mb-2 text-gray-300" />
                <p>Add your skills to showcase your expertise to potential employers.</p>
              </div>
            )}
          </ProfileSection>
          {/* Work Experience Section */}
          <ProfileSection
            icon={<FaBriefcase />} title="Work Experience"
            actionLabel={profile.work.length ? 'Edit Work Experience' : 'Add Work Experience'}
            onEdit={() => handleEdit('work')}
            isEditing={editSection === 'work'}
          >
            {editSection === 'work' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your work experience (one per line)"
                  value={temp.work ? temp.work.join('\n') : ''}
                  onChange={e => handleTempChange('work', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('work')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.work.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.work.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No work experience added yet.</div>
            )}
          </ProfileSection>
          {/* Education Section */}
          <ProfileSection
            icon={<FaGraduationCap />} title="Education"
            actionLabel={profile.education.length ? 'Edit Education' : 'Add Education'}
            onEdit={() => handleEdit('education')}
            isEditing={editSection === 'education'}
          >
            {editSection === 'education' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your education (one per line)"
                  value={temp.education ? temp.education.join('\n') : ''}
                  onChange={e => handleTempChange('education', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('education')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.education.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.education.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No education added yet.</div>
            )}
          </ProfileSection>
          {/* Responsibilities Section */}
          <ProfileSection
            icon={<FaTasks />} title="Responsibilities"
            actionLabel={profile.responsibilities.length ? 'Edit Responsibility' : 'Add Responsibility'}
            onEdit={() => handleEdit('responsibilities')}
            isEditing={editSection === 'responsibilities'}
          >
            {editSection === 'responsibilities' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your responsibilities (one per line)"
                  value={temp.responsibilities ? temp.responsibilities.join('\n') : ''}
                  onChange={e => handleTempChange('responsibilities', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('responsibilities')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.responsibilities.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.responsibilities.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No responsibilities added yet.</div>
            )}
          </ProfileSection>
          {/* Certificates Section */}
          <ProfileSection
            icon={<FaCertificate />} title="Certificates"
            actionLabel={profile.certificates.length ? 'Edit Certificate' : 'Add Certificate'}
            onEdit={() => handleEdit('certificates')}
            isEditing={editSection === 'certificates'}
          >
            {editSection === 'certificates' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your certificates (one per line)"
                  value={temp.certificates ? temp.certificates.join('\n') : ''}
                  onChange={e => handleTempChange('certificates', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('certificates')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
              </div>
            ) : profile.certificates.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.certificates.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No certificates added yet.</div>
            )}
          </ProfileSection>
          {/* Projects Section */}
          <ProfileSection
            icon={<FaProjectDiagram />} title="Projects"
            actionLabel={profile.projects.length ? 'Edit Project' : 'Add Project'}
            onEdit={() => handleEdit('projects')}
            isEditing={editSection === 'projects'}
          >
            {editSection === 'projects' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your projects (one per line)"
                  value={temp.projects ? temp.projects.join('\n') : ''}
                  onChange={e => handleTempChange('projects', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('projects')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                </div>
          </div>
            ) : profile.projects.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.projects.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No projects added yet.</div>
            )}
          </ProfileSection>
          {/* Achievements Section */}
          <ProfileSection
            icon={<FaTrophy />} title="Achievements"
            actionLabel={profile.achievements.length ? 'Edit Achievement' : 'Add Achievement'}
            onEdit={() => handleEdit('achievements')}
            isEditing={editSection === 'achievements'}
          >
            {editSection === 'achievements' ? (
              <div className="flex flex-col gap-2">
                <textarea
                  className="border rounded p-2 w-full"
                  rows={3}
                  placeholder="Describe your achievements (one per line)"
                  value={temp.achievements ? temp.achievements.join('\n') : ''}
                  onChange={e => handleTempChange('achievements', e.target.value.split('\n').map(s => s.trim()).filter(Boolean))}
                />
                <div className="flex gap-2 mt-2">
                  <button className="bg-[#23414c] text-white px-4 py-1 rounded" onClick={() => handleSave('achievements')}>Save</button>
                  <button className="bg-gray-100 text-[#23414c] px-4 py-1 rounded" onClick={handleCancel}>Cancel</button>
                  </div>
              </div>
            ) : profile.achievements.length ? (
              <ul className="list-disc ml-6 text-gray-700">
                {profile.achievements.map((item, idx) => <li key={idx}>{item}</li>)}
              </ul>
            ) : (
              <div className="text-gray-400">No achievements added yet.</div>
            )}
          </ProfileSection>
          {/* Social Links */}
          <ProfileSection
            icon={<FaShareAlt />}
            title="Social Links"
            actionLabel="Manage Links"
            onEdit={() => {}}
            isEditing={false}
          >
            <div className="space-y-4">
              {/* Display Social Links */}
              {profile.social?.length > 0 && (
                <div className="flex flex-wrap gap-3">
                  {profile.social.map((s, i) => (
                    <a
                      key={i}
                      href={s.url}
                      className="flex items-center gap-2 bg-gray-50 hover:bg-[#23414c]/10 px-4 py-2 rounded-lg transition-colors group"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <span className="text-[#23414c] text-lg group-hover:scale-110 transition-transform">{s.icon}</span>
                      <span className="text-sm text-gray-600 group-hover:text-[#23414c]">
                        {s.url.replace(/^https?:\/\//, '').split('/')[0]}
                      </span>
                    </a>
                  ))}
                </div>
              )}

              {/* Add New Social Link */}
              <div className="border-t pt-4">
                <h4 className="text-sm font-semibold text-gray-700 mb-3">Add New Link</h4>
                <div className="space-y-3">
                  <div className="flex gap-3">
                    <select
                      className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#23414c]/20 focus:border-[#23414c]"
                      value={temp.socialIcon || ''}
                      onChange={e => setTemp({ ...temp, socialIcon: e.target.value })}
                    >
                      <option value="">Select Platform</option>
                      <option value="linkedin">LinkedIn</option>
                      <option value="github">GitHub</option>
                      <option value="twitter">Twitter</option>
                      <option value="instagram">Instagram</option>
                      <option value="facebook">Facebook</option>
                      <option value="globe">Website</option>
                      <option value="envelope">Email</option>
                    </select>
                    <input
                      className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm placeholder-gray-400 focus:ring-2 focus:ring-[#23414c]/20 focus:border-[#23414c]"
                      placeholder="Enter URL"
                      value={temp.socialUrl || ''}
                      onChange={e => setTemp({ ...temp, socialUrl: e.target.value })}
                    />
                    <button
                      className="bg-[#23414c] text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-[#23414c]/90 transition-colors disabled:opacity-50"
                      disabled={!temp.socialIcon || !temp.socialUrl}
                      onClick={() => {
                        if (temp.socialIcon && temp.socialUrl) {
                          const iconMap = {
                            linkedin: <FaLinkedin />,
                            github: <FaGithub />,
                            twitter: <FaTwitter />,
                            instagram: <FaInstagram />,
                            facebook: <FaFacebook />,
                            globe: <FaGlobe />,
                            envelope: <FaEnvelope />,
                          };
                          updateProfile({
                            social: [
                              ...profile.social,
                              { icon: iconMap[temp.socialIcon], url: temp.socialUrl },
                            ],
                          });
                          setTemp({ ...temp, socialIcon: '', socialUrl: '' });
                        }
                      }}
                    >
                      Add
                    </button>
                  </div>
                </div>
              </div>

              {/* Manage Existing Links */}
              {profile.social?.length > 0 && (
                <div className="border-t pt-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-3">Manage Links</h4>
                  <div className="space-y-2">
                    {profile.social.map((s, i) => (
                      <div key={i} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-lg">
                        <div className="flex items-center gap-2">
                          <span className="text-[#23414c]">{s.icon}</span>
                          <span className="text-sm text-gray-600 truncate max-w-xs">{s.url}</span>
                        </div>
                        <button
                          className="text-red-500 hover:text-red-700 text-sm font-medium transition-colors"
                          onClick={() => updateProfile({
                            social: profile.social.filter((_, idx) => idx !== i),
                          })}
                        >
                          Remove
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </ProfileSection>
          {/* Streaks */}
          {/* Streaks section removed */}
        </div>
          {/* Sidebar */}
          <div className="xl:w-80 flex-shrink-0 space-y-6">
            {/* Quick Actions Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button
                  className="w-full bg-[#23414c] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#23414c]/90 transition-all duration-200 flex items-center justify-center gap-2"
                  onClick={() => {
                    alert('Profile saved!');
                  }}
                >
                  <FaUserEdit />
                  Save Profile
                </button>
                <button
                  className="w-full bg-white border-2 border-[#23414c] text-[#23414c] px-6 py-3 rounded-lg font-semibold hover:bg-[#23414c]/5 transition-all duration-200 flex items-center justify-center gap-2"
                  onClick={() => setShowProfileModal(true)}
                >
                  <FaShareAlt />
                  View Profile
                </button>
              </div>
            </div>

            {/* Career Goals Card */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200 p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                  <FaStar className="text-blue-600" />
                </div>
                <h3 className="text-lg font-bold text-gray-800">Career Goals</h3>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Define your career objectives and let opportunities find you.
              </p>
              <button
                className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-200 flex items-center justify-center gap-2"
                onClick={() => navigate('/student/jobs')}
              >
                <FaBriefcase />
                Find Jobs
              </button>
            </div>

            {/* Profile Strength Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Profile Strength</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Completion</span>
                  <span className="text-sm font-semibold text-[#23414c]">{progress}%</span>
                </div>
                <div className="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-3 bg-gradient-to-r from-[#23414c] to-blue-600 rounded-full transition-all duration-500"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
                <div className="grid grid-cols-2 gap-3 pt-2">
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-800">{profile.skills?.length || 0}</div>
                    <div className="text-xs text-gray-500">Skills</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-800">{profile.projects?.length || 0}</div>
                    <div className="text-xs text-gray-500">Projects</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Tips Card */}
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-200 p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center">
                  <FaTrophy className="text-green-600" />
                </div>
                <h3 className="text-lg font-bold text-gray-800">Pro Tips</h3>
              </div>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-1">•</span>
                  Complete your profile to increase visibility
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-1">•</span>
                  Add relevant skills and certifications
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-1">•</span>
                  Showcase your best projects
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Profile Modal */}
      {showProfileModal && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl p-8 max-w-2xl w-full relative">
            <button
              className="absolute top-2 right-2 text-2xl text-gray-400 hover:text-[#23414c]"
              onClick={() => setShowProfileModal(false)}
              title="Close"
            >
              &times;
            </button>
            <h2 className="text-2xl font-bold mb-4 text-[#23414c]">Saved Profile</h2>
            <div className="flex flex-col gap-2">
              <div><span className="font-bold">Name:</span> {profile.name}</div>
              <div><span className="font-bold">Email:</span> {profile.email}</div>
              <div><span className="font-bold">Address:</span> {profile.address}</div>
              {profile.resumeFile && (
                <div><span className="font-bold">Resume:</span> <a href={profile.resumeFile.data} download={profile.resumeFile.name} className="text-blue-700 underline">{profile.resumeFile.name}</a></div>
              )}
              {profile.resume && <div><span className="font-bold">Resume Link/Desc:</span> {profile.resume}</div>}
              <div><span className="font-bold">About:</span> {profile.about}</div>
              <div><span className="font-bold">Skills:</span> {Array.isArray(profile.skills) ? profile.skills.join(', ') : profile.skills}</div>
              <div><span className="font-bold">Work:</span> {Array.isArray(profile.work) ? profile.work.join('; ') : profile.work}</div>
              <div><span className="font-bold">Education:</span> {Array.isArray(profile.education) ? profile.education.join('; ') : profile.education}</div>
              <div><span className="font-bold">Responsibilities:</span> {Array.isArray(profile.responsibilities) ? profile.responsibilities.join('; ') : profile.responsibilities}</div>
              <div><span className="font-bold">Certificates:</span> {Array.isArray(profile.certificates) ? profile.certificates.join('; ') : profile.certificates}</div>
              <div><span className="font-bold">Projects:</span> {Array.isArray(profile.projects) ? profile.projects.join('; ') : profile.projects}</div>
              <div><span className="font-bold">Achievements:</span> {Array.isArray(profile.achievements) ? profile.achievements.join('; ') : profile.achievements}</div>
              <div><span className="font-bold">Social Links:</span> {profile.social && profile.social.map((s, i) => <a key={i} href={s.url} className="text-blue-700 underline ml-2" target="_blank" rel="noopener noreferrer">{s.url}</a>)}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

function ProfileSection({ icon, title, actionLabel, onEdit, children, isEditing }) {
  return (
    <div className={`bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 ${isEditing ? 'ring-2 ring-[#23414c]/20 shadow-lg' : ''}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-[#23414c]/10 flex items-center justify-center">
              <span className="text-[#23414c] text-lg">{icon}</span>
            </div>
            <h3 className="text-xl font-bold text-gray-800">{title}</h3>
          </div>
          <button
            className="flex items-center gap-2 text-[#23414c] hover:bg-[#23414c]/10 px-3 py-2 rounded-lg transition-all duration-200 text-sm font-semibold group"
            onClick={onEdit}
          >
            <FaEdit className="group-hover:scale-110 transition-transform" />
            {actionLabel}
          </button>
        </div>
        <div className="space-y-3">
          {children}
        </div>
      </div>
    </div>
  );
}

export default Profile;