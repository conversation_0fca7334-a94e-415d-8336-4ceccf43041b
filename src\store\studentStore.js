import { create } from 'zustand';
import axios from 'axios';
import { STUDENT_ENDPOINTS } from '../lib/constants';

const axiosInstance = axios.create({
    withCredentials: true,
});

axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Add response interceptor to handle token expiration
axiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            localStorage.removeItem('token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

// Default profile structure
const defaultProfile = {
    name: '',
    email: '',
    address: '',
    avatar: '',
    about: '',
    resume: '',
    skills: [],
    work: [],
    education: [],
    responsibilities: [],
    certificates: [],
    projects: [],
    achievements: [],
    social: [],
    streaks: [],
    // Additional fields from API
    _id: '',
    completionStatus: 0,
    isPublished: false,
    template: 'default',
    headline: ''
};

const useStudentStore = create((set, get) => ({
    // Loading states
    loading: false,
    profileLoading: false,
    jobsLoading: false,
    applicationsLoading: false,
    testsLoading: false,

    // Error states
    error: null,
    profileError: null,
    jobsError: null,
    applicationsError: null,
    testsError: null,

    // Data states
    profile: defaultProfile,
    jobs: [],
    jobDetails: null,
    applications: [],
    applicationDetails: null,
    tests: {
        upcoming: [],
        live: [],
        history: []
    },
    testDetails: null,
    results: [],

    // Pagination and filters
    jobsFilter: {
        tech: '',
        category: '',
        jobType: '',
        experienceLevel: '',
        workMode: '',
        location: '',
        salaryMin: '',
        salaryMax: '',
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
    },
    jobsPagination: {
        currentPage: 1,
        totalPages: 0,
        totalJobs: 0,
        hasNextPage: false,
        hasPrevPage: false,
        limit: 10
    },

    // Basic setters
    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),
    clearError: () => set({ error: null, profileError: null, jobsError: null, applicationsError: null, testsError: null }),

    // Profile Management
    setProfile: (profile) => set({ profile }),
    updateProfile: (updates) => set((state) => ({
        profile: { ...state.profile, ...updates }
    })),

    // Profile API methods
    fetchProfile: async () => {
        set({ profileLoading: true, profileError: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.PROFILE);
            const apiResponse = data.success ? data.data : data;

            // Debug: Log the API response to understand the structure
            console.log('API Response:', apiResponse);

            // Map API response to expected profile structure
            const profileData = {
                // Basic info mapping
                name: apiResponse.profile?.Title || '',
                email: apiResponse.profile?.Email || '',
                address: apiResponse.profile?.Website || '', // Using Website as address for now
                avatar: apiResponse.profile?.ProfilePic || '',
                about: apiResponse.profile?.summery || '', // Note: API uses 'summery' not 'summary'
                resume: apiResponse.profile?.PDFLink || '',

                // Skills mapping - convert from API format to simple array
                skills: apiResponse.profile?.Skills?.map(skill => skill.Skill) || [],

                // Work experience mapping
                work: apiResponse.profile?.Experience?.map(exp =>
                    `${exp.Position} at ${exp.Company} (${exp.StartDate} - ${exp.EndDate || 'Present'})`
                ) || [],

                // Education mapping
                education: apiResponse.profile?.Education?.map(edu =>
                    `${edu.Degree} from ${edu.Institution} (${edu.StartDate} - ${edu.EndDate || 'Present'})`
                ) || [],

                // Projects mapping
                projects: apiResponse.profile?.Projects?.map(project =>
                    `${project.Name}: ${project.Description}`
                ) || [],

                // Certifications mapping
                certificates: apiResponse.profile?.Certifications?.map(cert =>
                    `${cert.Name} - ${cert.Issuer} (${cert.Date})`
                ) || [],

                // Awards as achievements
                achievements: apiResponse.profile?.Awards?.map(award =>
                    `${award.Title}: ${award.Description}`
                ) || [],

                // Responsibilities (using Volunteering data)
                responsibilities: apiResponse.profile?.Volunteering?.map(vol =>
                    `${vol.Role} at ${vol.Organization}: ${vol.Description}`
                ) || [],

                // Social links (placeholder structure)
                social: [],

                // Streaks (placeholder)
                streaks: [],

                // Additional fields from API
                _id: apiResponse.profile?._id,
                completionStatus: apiResponse.completionStatus || 0,
                isPublished: apiResponse.profile?.isPublished || false,
                template: apiResponse.profile?.Template || 'default'
            };

            // Debug: Log the mapped profile data
            console.log('Mapped Profile Data:', profileData);

            // Store both mapped data and raw API data for resume template
            const finalProfile = {
                ...defaultProfile,
                ...profileData,
                apiData: apiResponse // Store raw API response for resume template
            };

            set({
                profile: finalProfile,
                profileLoading: false
            });
            return finalProfile;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch profile';
            set({
                profileError: errorMsg,
                profileLoading: false
            });
            return null;
        }
    },

    saveProfile: async (profileData) => {
        set({ profileLoading: true, profileError: null });
        try {
            // Map profile data back to API format
            const apiPayload = {
                Title: profileData.name || get().profile.name,
                Email: profileData.email || get().profile.email,
                Website: profileData.address || get().profile.address,
                summery: profileData.about || get().profile.about, // Note: API uses 'summery'
                Headline: profileData.headline || get().profile.headline || '',

                // Handle skills - convert array to API format
                Skills: profileData.skills ? profileData.skills.map(skill => ({
                    Skill: skill,
                    Proficiency: 'Intermediate', // Default proficiency
                    Keywords: [],
                    Description: ''
                })) : undefined,

                // Handle other fields if they exist in the update
                ...(profileData.avatar && { ProfilePic: profileData.avatar }),
                ...(profileData.resume && { PDFLink: profileData.resume })
            };

            // Remove undefined values
            Object.keys(apiPayload).forEach(key => {
                if (apiPayload[key] === undefined) {
                    delete apiPayload[key];
                }
            });

            const { data } = await axiosInstance.put(STUDENT_ENDPOINTS.PROFILE, apiPayload);

            // After successful save, fetch the updated profile to ensure consistency
            await get().fetchProfile();

            set({ profileLoading: false });
            return data;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to save profile';
            set({
                profileError: errorMsg,
                profileLoading: false
            });
            throw error;
        }
    },

    // Jobs Management
    setJobsFilter: (filter) => set((state) => ({
        jobsFilter: { ...state.jobsFilter, ...filter }
    })),

    fetchJobs: async (filters = {}) => {
        set({ jobsLoading: true, jobsError: null });
        try {
            const currentFilter = { ...get().jobsFilter, ...filters };
            const queryParams = new URLSearchParams();

            Object.entries(currentFilter).forEach(([key, value]) => {
                if (value !== '' && value !== null && value !== undefined) {
                    queryParams.append(key, value);
                }
            });

            const { data } = await axiosInstance.get(`${STUDENT_ENDPOINTS.JOBS}?${queryParams}`);

            if (data.success) {
                set({
                    jobs: data.data.jobs,
                    jobsPagination: data.data.pagination,
                    jobsFilter: currentFilter,
                    jobsLoading: false
                });
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch jobs';
            set({
                jobsError: errorMsg,
                jobsLoading: false
            });
            return null;
        }
    },

    fetchJobDetails: async (jobId) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.JOB_DETAILS(jobId));
            const jobDetails = data.success ? data.data : data;
            set({
                jobDetails,
                loading: false
            });
            return jobDetails;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch job details';
            set({
                error: errorMsg,
                loading: false
            });
            return null;
        }
    },

    applyToJob: async (jobId) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.post(STUDENT_ENDPOINTS.APPLY_TO_JOB(jobId));

            // Update the job details if it's currently loaded
            const currentJobDetails = get().jobDetails;
            if (currentJobDetails && currentJobDetails.job._id === jobId) {
                set({
                    jobDetails: {
                        ...currentJobDetails,
                        hasApplied: true,
                        canApply: false
                    }
                });
            }

            // Refresh applications
            get().fetchApplications();

            set({ loading: false });
            return data;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to apply to job';
            set({
                error: errorMsg,
                loading: false
            });
            throw error;
        }
    },

    // Applications Management
    fetchApplications: async (filters = {}) => {
        set({ applicationsLoading: true, applicationsError: null });
        try {
            const queryParams = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== '' && value !== null && value !== undefined) {
                    queryParams.append(key, value);
                }
            });

            const { data } = await axiosInstance.get(`${STUDENT_ENDPOINTS.APPLICATIONS}?${queryParams}`);

            if (data.success) {
                set({
                    applications: data.data.applications,
                    applicationsLoading: false
                });
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch applications';
            set({
                applicationsError: errorMsg,
                applicationsLoading: false
            });
            return null;
        }
    },

    fetchApplicationDetails: async (applicationId) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.APPLICATION_DETAILS(applicationId));
            const applicationDetails = data.success ? data.data : data;
            set({
                applicationDetails,
                loading: false
            });
            return applicationDetails;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch application details';
            set({
                error: errorMsg,
                loading: false
            });
            return null;
        }
    },

    // Tests Management
    fetchUpcomingTests: async () => {
        set({ testsLoading: true, testsError: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.UPCOMING_TESTS);

            if (data.success) {
                set((state) => ({
                    tests: { ...state.tests, upcoming: data.data },
                    testsLoading: false
                }));
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch upcoming tests';
            set({
                testsError: errorMsg,
                testsLoading: false
            });
            return null;
        }
    },

    fetchLiveTests: async () => {
        set({ testsLoading: true, testsError: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.LIVE_TESTS);

            if (data.success) {
                set((state) => ({
                    tests: { ...state.tests, live: data.data },
                    testsLoading: false
                }));
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch live tests';
            set({
                testsError: errorMsg,
                testsLoading: false
            });
            return null;
        }
    },

    fetchTestHistory: async () => {
        set({ testsLoading: true, testsError: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.TEST_HISTORY);

            if (data.success) {
                set((state) => ({
                    tests: { ...state.tests, history: data.data },
                    testsLoading: false
                }));
                return data.data;
            }
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch test history';
            set({
                testsError: errorMsg,
                testsLoading: false
            });
            return null;
        }
    },

    fetchTestDetails: async (testId) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.TEST_DETAILS(testId));
            const testDetails = data.success ? data.data : data;
            set({
                testDetails,
                loading: false
            });
            return testDetails;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch test details';
            set({
                error: errorMsg,
                loading: false,
                testDetails: null
            });
            return null;
        }
    },

    startTest: async (testId) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.post(STUDENT_ENDPOINTS.START_TEST(testId));
            set({ loading: false });
            return data;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to start test';
            set({
                error: errorMsg,
                loading: false
            });
            throw error;
        }
    },

    submitTest: async (testId, answers, additionalData = {}) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.post(STUDENT_ENDPOINTS.SUBMIT_TEST(testId), {
                answers,
                ...additionalData
            });

            // Refresh test history after submission
            get().fetchTestHistory();

            set({ loading: false });
            return data;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to submit test';
            set({
                error: errorMsg,
                loading: false
            });
            throw error;
        }
    },

    // Results Management
    fetchResults: async () => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.get(STUDENT_ENDPOINTS.RESULTS);
            const results = data.success ? data.data : data;
            set({
                results,
                loading: false
            });
            return results;
        } catch (error) {
            const errorMsg = error?.response?.data?.message || 'Failed to fetch results';
            set({
                error: errorMsg,
                loading: false
            });
            return null;
        }
    },

    // Utility methods
    getProfileCompletion: () => {
        const profile = get().profile;
        const sectionKeys = ['name', 'email', 'address', 'about', 'resume', 'skills', 'work', 'education', 'responsibilities', 'certificates', 'projects', 'achievements'];

        const filledSections = sectionKeys.filter(key => {
            const val = profile[key];
            if (Array.isArray(val)) return val.length > 0;
            return !!val && val !== '';
        }).length;

        return Math.round((filledSections / sectionKeys.length) * 100);
    },

    // Reset methods
    resetJobs: () => set({ jobs: [], jobDetails: null, jobsPagination: { currentPage: 1, totalPages: 0, totalJobs: 0, hasNextPage: false, hasPrevPage: false, limit: 10 } }),
    resetApplications: () => set({ applications: [] }),
    resetTests: () => set({ tests: { upcoming: [], live: [], history: [] } }),
    resetResults: () => set({ results: [] }),
    resetAll: () => set({
        profile: defaultProfile,
        jobs: [],
        jobDetails: null,
        applications: [],
        tests: { upcoming: [], live: [], history: [] },
        results: [],
        error: null,
        profileError: null,
        jobsError: null,
        applicationsError: null,
        testsError: null
    })
}));

export default useStudentStore;