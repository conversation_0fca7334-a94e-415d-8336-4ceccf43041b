import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import useAuth from '../../hooks/useAuth';

/**
 * Demo component to test authentication and role-based access
 * This component helps verify that the protected routing system works correctly
 */
const AuthDemo = () => {
  const [testCredentials, setTestCredentials] = useState({
    email: '',
    password: ''
  });

  const {
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    error,
    isAdmin,
    isCompany,
    isStudent
  } = useAuth();

  const handleTestLogin = async (role) => {
    // Demo credentials for testing different roles
    const credentials = {
      admin: { email: '<EMAIL>', password: 'admin123' },
      company: { email: '<EMAIL>', password: 'company123' },
      student: { email: '<EMAIL>', password: 'student123' }
    };

    await login(credentials[role]);
  };

  const testRoutes = {
    public: [
      { name: 'Home', path: '/' },
      { name: '<PERSON><PERSON>', path: '/login' },
      { name: 'Register', path: '/register' }
    ],
    admin: [
      { name: 'Admin Dashboard', path: '/admin-dashboard' },
      { name: 'Admin Companies', path: '/admin-dashboard/companies' },
      { name: 'Admin Users', path: '/admin-dashboard/users' },
      { name: 'Admin Settings', path: '/admin-dashboard/settings' }
    ],
    company: [
      { name: 'Company Dashboard', path: '/dashboard' },
      { name: 'Create Job', path: '/job-create' },
      { name: 'Test Management', path: '/test-management' },
      { name: 'Questions', path: '/aptitude' },
      { name: 'Profile', path: '/profile' }
    ],
    student: [
      { name: 'Student Dashboard', path: '/student-dashboard' },
      { name: 'Take Test', path: '/test' },
      { name: 'Test Results', path: '/test-result' },
      { name: 'Interview Prep', path: '/interview-prep' },
      { name: 'Student Profile', path: '/student-profile' }
    ]
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
          Authentication & Role-Based Access Demo
        </h1>

        {/* Current Authentication Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Current Authentication Status</h2>
          
          {loading ? (
            <p className="text-blue-600">Loading...</p>
          ) : isAuthenticated ? (
            <div className="space-y-2">
              <p className="text-green-600 font-medium">✓ Authenticated</p>
              <p><strong>Email:</strong> {user?.email}</p>
              <p><strong>Name:</strong> {user?.name || 'N/A'}</p>
              <p><strong>Role:</strong> <span className="capitalize font-medium">{user?.role}</span></p>
              <p><strong>Role Checks:</strong></p>
              <ul className="ml-4 space-y-1">
                <li>Is Admin: {isAdmin ? '✓' : '✗'}</li>
                <li>Is Company: {isCompany ? '✓' : '✗'}</li>
                <li>Is Student: {isStudent ? '✓' : '✗'}</li>
              </ul>
              <button
                onClick={logout}
                className="mt-4 bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              >
                Logout
              </button>
            </div>
          ) : (
            <p className="text-red-600 font-medium">✗ Not Authenticated</p>
          )}

          {error && (
            <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              <strong>Error:</strong> {error}
            </div>
          )}
        </div>

        {/* Quick Login Buttons */}
        {!isAuthenticated && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Quick Test Login</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => handleTestLogin('admin')}
                disabled={loading}
                className="bg-purple-500 text-white px-4 py-3 rounded hover:bg-purple-600 disabled:opacity-50"
              >
                Login as Admin
              </button>
              <button
                onClick={() => handleTestLogin('company')}
                disabled={loading}
                className="bg-blue-500 text-white px-4 py-3 rounded hover:bg-blue-600 disabled:opacity-50"
              >
                Login as Company
              </button>
              <button
                onClick={() => handleTestLogin('student')}
                disabled={loading}
                className="bg-green-500 text-white px-4 py-3 rounded hover:bg-green-600 disabled:opacity-50"
              >
                Login as Student
              </button>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Note: These are demo credentials for testing purposes
            </p>
          </div>
        )}

        {/* Custom Login Form */}
        {!isAuthenticated && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Custom Login</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                type="email"
                placeholder="Email"
                value={testCredentials.email}
                onChange={(e) => setTestCredentials(prev => ({ ...prev, email: e.target.value }))}
                className="border rounded px-3 py-2"
              />
              <input
                type="password"
                placeholder="Password"
                value={testCredentials.password}
                onChange={(e) => setTestCredentials(prev => ({ ...prev, password: e.target.value }))}
                className="border rounded px-3 py-2"
              />
            </div>
            <button
              onClick={() => login(testCredentials)}
              disabled={loading || !testCredentials.email || !testCredentials.password}
              className="mt-4 bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 disabled:opacity-50"
            >
              {loading ? 'Logging in...' : 'Login'}
            </button>
          </div>
        )}

        {/* Route Testing */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Public Routes */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Public Routes (Always Accessible)</h2>
            <div className="space-y-2">
              {testRoutes.public.map(route => (
                <Link
                  key={route.path}
                  to={route.path}
                  className="block p-2 bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                >
                  {route.name} ({route.path})
                </Link>
              ))}
            </div>
          </div>

          {/* Role-Specific Routes */}
          <div className="space-y-6">
            {/* Admin Routes */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4 text-purple-600">
                Admin Routes {isAdmin ? '(Accessible)' : '(Restricted)'}
              </h2>
              <div className="space-y-2">
                {testRoutes.admin.map(route => (
                  <Link
                    key={route.path}
                    to={route.path}
                    className={`block p-2 rounded transition-colors ${
                      isAdmin 
                        ? 'bg-purple-100 hover:bg-purple-200 text-purple-800' 
                        : 'bg-gray-100 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {route.name} ({route.path})
                  </Link>
                ))}
              </div>
            </div>

            {/* Company Routes */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4 text-blue-600">
                Company Routes {isCompany ? '(Accessible)' : '(Restricted)'}
              </h2>
              <div className="space-y-2">
                {testRoutes.company.map(route => (
                  <Link
                    key={route.path}
                    to={route.path}
                    className={`block p-2 rounded transition-colors ${
                      isCompany 
                        ? 'bg-blue-100 hover:bg-blue-200 text-blue-800' 
                        : 'bg-gray-100 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {route.name} ({route.path})
                  </Link>
                ))}
              </div>
            </div>

            {/* Student Routes */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4 text-green-600">
                Student Routes {isStudent ? '(Accessible)' : '(Restricted)'}
              </h2>
              <div className="space-y-2">
                {testRoutes.student.map(route => (
                  <Link
                    key={route.path}
                    to={route.path}
                    className={`block p-2 rounded transition-colors ${
                      isStudent 
                        ? 'bg-green-100 hover:bg-green-200 text-green-800' 
                        : 'bg-gray-100 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {route.name} ({route.path})
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-8">
          <h2 className="text-xl font-semibold mb-4 text-yellow-800">Testing Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-yellow-700">
            <li>Use the quick login buttons to test different roles</li>
            <li>Try accessing routes that your current role shouldn't have access to</li>
            <li>Verify that you get redirected to appropriate dashboards</li>
            <li>Test logout functionality</li>
            <li>Try accessing protected routes while not authenticated</li>
            <li>Check that navigation menu updates based on your role</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default AuthDemo;
