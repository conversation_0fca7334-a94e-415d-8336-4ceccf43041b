import React, { useState } from 'react';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import QuestionFilterAndBundle from './QuestionFilterAndBundle';

const ReactErrorFixes = () => {
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [testResults, setTestResults] = useState(null);

  // Test data to verify the fixes
  const mockQuestionCategories = [
    { category: 'Frontend', count: 15 },
    { category: 'Backend', count: 20 },
    { category: 'Database', count: 10 },
    'JavaScript', // Mixed format to test compatibility
    'React'
  ];

  const mockQuestions = [
    {
      _id: 'q1',
      questionText: 'What is React?',
      category: 'Frontend',
      difficulty: 'Easy',
      type: 'MCQ',
      options: ['Library', 'Framework', 'Language', 'Tool'],
      correctAnswer: 0,
      points: 1,
      tags: ['react', 'frontend']
    },
    {
      _id: 'q2',
      questionText: 'What is Node.js?',
      category: 'Backend',
      difficulty: 'Medium',
      type: 'MCQ',
      options: ['Runtime', 'Framework', 'Language', 'Database'],
      correctAnswer: 0,
      points: 2,
      tags: ['nodejs', 'backend']
    }
  ];

  const testCategoryRendering = () => {
    console.log('Testing category rendering with mixed formats:');
    
    mockQuestionCategories.forEach((categoryItem, index) => {
      const categoryName = typeof categoryItem === 'string' ? categoryItem : categoryItem.category;
      const categoryCount = typeof categoryItem === 'object' ? categoryItem.count : null;
      
      console.log(`Category ${index + 1}: ${categoryName}${categoryCount ? ` (${categoryCount})` : ''}`);
    });

    setTestResults({
      test: 'Category Rendering',
      status: 'success',
      message: 'Categories rendered successfully with mixed formats',
      data: mockQuestionCategories
    });

    toast.success('Category rendering test passed!');
  };

  const testQuestionKeys = () => {
    console.log('Testing question key generation:');
    
    const keys = mockQuestions.map((question, index) => {
      const category = question.category || 'Uncategorized';
      const key = `${category}-question-${question._id || question.id || `${category}-${index}`}`;
      console.log(`Question ${index + 1} key: ${key}`);
      return key;
    });

    // Check for duplicate keys
    const uniqueKeys = new Set(keys);
    const hasDuplicates = keys.length !== uniqueKeys.size;

    setTestResults({
      test: 'Question Key Generation',
      status: hasDuplicates ? 'error' : 'success',
      message: hasDuplicates ? 'Duplicate keys found!' : 'All keys are unique',
      data: { keys, uniqueKeys: Array.from(uniqueKeys), hasDuplicates }
    });

    if (hasDuplicates) {
      toast.error('Duplicate keys found!');
    } else {
      toast.success('All question keys are unique!');
    }
  };

  const testObjectRendering = () => {
    console.log('Testing object rendering prevention:');
    
    try {
      // This should NOT be rendered directly
      const objectToRender = { category: 'Frontend', count: 15 };
      
      // Instead, we should extract the values
      const safeRender = `${objectToRender.category} (${objectToRender.count})`;
      
      console.log('Object:', objectToRender);
      console.log('Safe render:', safeRender);

      setTestResults({
        test: 'Object Rendering Prevention',
        status: 'success',
        message: 'Objects are properly converted to strings before rendering',
        data: { original: objectToRender, safeRender }
      });

      toast.success('Object rendering test passed!');
    } catch (error) {
      setTestResults({
        test: 'Object Rendering Prevention',
        status: 'error',
        message: error.message,
        data: { error: error.toString() }
      });

      toast.error('Object rendering test failed!');
    }
  };

  const testFilterResponseHandling = () => {
    console.log('Testing filter response handling:');
    
    // Test both response formats
    const arrayResponse = mockQuestions;
    const objectResponse = { questions: mockQuestions, pagination: { total: 2 } };
    const emptyResponse = null;

    const results = [
      {
        type: 'Array Response',
        input: arrayResponse,
        output: Array.isArray(arrayResponse) ? arrayResponse : (arrayResponse?.questions || [])
      },
      {
        type: 'Object Response',
        input: objectResponse,
        output: Array.isArray(objectResponse) ? objectResponse : (objectResponse?.questions || [])
      },
      {
        type: 'Empty Response',
        input: emptyResponse,
        output: Array.isArray(emptyResponse) ? emptyResponse : (emptyResponse?.questions || [])
      }
    ];

    console.log('Response handling results:', results);

    setTestResults({
      test: 'Filter Response Handling',
      status: 'success',
      message: 'All response formats handled correctly',
      data: results
    });

    toast.success('Filter response handling test passed!');
  };

  const handleAddToTest = (questions) => {
    console.log('Questions added to test:', questions);
    toast.success(`Added ${questions.length} questions to test`);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">React Error Fixes Test Suite</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Test Buttons */}
        <div className="space-y-3">
          <button
            onClick={testCategoryRendering}
            className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Test Category Rendering
          </button>
          
          <button
            onClick={testQuestionKeys}
            className="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            Test Question Keys
          </button>
          
          <button
            onClick={testObjectRendering}
            className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          >
            Test Object Rendering
          </button>
          
          <button
            onClick={testFilterResponseHandling}
            className="w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors"
          >
            Test Filter Response Handling
          </button>
          
          <button
            onClick={() => setShowQuestionModal(true)}
            className="w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
          >
            Test QuestionFilterAndBundle Component
          </button>
        </div>

        {/* Test Results */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Test Results</h3>
          {testResults ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="font-medium">Test:</span>
                <span>{testResults.test}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium">Status:</span>
                <span className={`px-2 py-1 rounded text-sm ${
                  testResults.status === 'success' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {testResults.status}
                </span>
              </div>
              <div>
                <span className="font-medium">Message:</span>
                <p className="text-gray-600 mt-1">{testResults.message}</p>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No tests run yet</p>
          )}
        </div>
      </div>

      {/* Detailed Results */}
      {testResults && testResults.data && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Detailed Results</h3>
          <pre className="bg-gray-100 p-4 rounded-lg overflow-auto max-h-96 text-sm">
            {JSON.stringify(testResults.data, null, 2)}
          </pre>
        </div>
      )}

      {/* QuestionFilterAndBundle Modal */}
      {showQuestionModal && (
        <QuestionFilterAndBundle
          isOpen={showQuestionModal}
          onClose={() => setShowQuestionModal(false)}
          onAddToTest={handleAddToTest}
          existingQuestions={[]}
        />
      )}
    </div>
  );
};

export default ReactErrorFixes;
