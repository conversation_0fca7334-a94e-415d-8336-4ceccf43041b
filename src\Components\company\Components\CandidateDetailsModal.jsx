import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    XMarkIcon,
    UserIcon,
    EnvelopeIcon,
    PhoneIcon,
    MapPinIcon,
    GlobeAltIcon,
    AcademicCapIcon,
    BriefcaseIcon,
    DocumentTextIcon,
    TrophyIcon,
    StarIcon,
    LinkIcon,
    CalendarIcon,
    BuildingOfficeIcon,
    LanguageIcon,
    HeartIcon,
    BookOpenIcon,
    UserGroupIcon,
    ChartBarIcon
} from '@heroicons/react/24/outline';
import ResumeTemplate from './ResumeTemplate';

const CandidateDetailsModal = ({ isOpen, onClose, candidateData, job }) => {
    const [activeTab, setActiveTab] = useState('overview');

    if (!isOpen || !candidateData) return null;

    const { candidate, resume, application, testResults } = candidateData;

    const tabs = [
        { id: 'overview', label: 'Overview', icon: UserIcon },
        { id: 'resume', label: 'Resume', icon: DocumentTextIcon },
        { id: 'test', label: 'Test Results', icon: ChartBarIcon }
    ];

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <motion.div
                className="bg-white rounded-2xl shadow-2xl w-full max-w-7xl h-[95vh] flex flex-col overflow-hidden"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
            >
                {/* Header */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-2xl font-bold">
                                {candidate?.name?.charAt(0)?.toUpperCase() || 'C'}
                            </div>
                            <div>
                                <h2 className="text-2xl font-bold">{candidate?.name}</h2>
                                <p className="text-blue-100">{resume?.Headline || 'Candidate'}</p>
                                <p className="text-blue-200 text-sm">Applied for: {job?.title}</p>
                            </div>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                        >
                            <XMarkIcon className="w-6 h-6" />
                        </button>
                    </div>

                    {/* Tabs */}
                    <div className="mt-6 flex gap-1">
                        {tabs.map((tab) => {
                            const Icon = tab.icon;
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id)}
                                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                                        activeTab === tab.id
                                            ? 'bg-white text-blue-600'
                                            : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'
                                    }`}
                                >
                                    <Icon className="w-4 h-4" />
                                    {tab.label}
                                </button>
                            );
                        })}
                    </div>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-y-auto min-h-0">
                    <AnimatePresence mode="wait">
                        {activeTab === 'overview' && (
                            <motion.div
                                key="overview"
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0, x: -20 }}
                                transition={{ duration: 0.2 }}
                            >
                                <OverviewTab
                                    candidate={candidate}
                                    resume={resume}
                                    application={application}
                                    testResults={testResults}
                                />
                            </motion.div>
                        )}
                        {activeTab === 'resume' && (
                            <motion.div
                                key="resume"
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0, x: -20 }}
                                transition={{ duration: 0.2 }}
                            >
                                <ResumeTab resume={resume} />
                            </motion.div>
                        )}
                        {activeTab === 'test' && (
                            <motion.div
                                key="test"
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0, x: -20 }}
                                transition={{ duration: 0.2 }}
                            >
                                <TestResultsTab testResults={testResults} />
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            </motion.div>
        </div>
    );
};

// Overview Tab Component
const OverviewTab = ({ candidate, resume, application, testResults }) => {
    return (
        <motion.div
            className="p-6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
        >
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left Column - Basic Info */}
                <div className="lg:col-span-1 space-y-6">
                    {/* Contact Information */}
                    <div className="bg-gray-50 rounded-xl p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                        <div className="space-y-3">
                            <div className="flex items-center gap-3">
                                <EnvelopeIcon className="w-5 h-5 text-gray-400" />
                                <span className="text-gray-700">{candidate?.email}</span>
                            </div>
                            {resume?.Phone && (
                                <div className="flex items-center gap-3">
                                    <PhoneIcon className="w-5 h-5 text-gray-400" />
                                    <span className="text-gray-700">{resume.Phone}</span>
                                </div>
                            )}
                            {resume?.Location && (
                                <div className="flex items-center gap-3">
                                    <MapPinIcon className="w-5 h-5 text-gray-400" />
                                    <span className="text-gray-700">{resume.Location}</span>
                                </div>
                            )}
                            {resume?.Website && (
                                <div className="flex items-center gap-3">
                                    <GlobeAltIcon className="w-5 h-5 text-gray-400" />
                                    <a 
                                        href={resume.Website} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800"
                                    >
                                        {resume.Website}
                                    </a>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Application Status */}
                    <div className="bg-gray-50 rounded-xl p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Application Status</h3>
                        <div className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-gray-600">Status:</span>
                                <span className="font-medium text-gray-900">
                                    {application?.status?.replace('_', ' ').toUpperCase() || 'Applied'}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-600">Applied:</span>
                                <span className="font-medium text-gray-900">
                                    {new Date(application?.appliedAt).toLocaleDateString()}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-600">Experience:</span>
                                <span className="font-medium text-gray-900">
                                    {candidate?.totalExperience || 0} years
                                </span>
                            </div>
                            {testResults?.status && (
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Test Status:</span>
                                    <span className="font-medium text-gray-900">
                                        {testResults.status.replace('_', ' ').toUpperCase()}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Social Profiles */}
                    {resume?.Profiles && resume.Profiles.length > 0 && (
                        <div className="bg-gray-50 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Social Profiles</h3>
                            <div className="space-y-3">
                                {resume.Profiles.map((profile, index) => (
                                    <div key={index} className="flex items-center gap-3">
                                        <img 
                                            src={profile.ProfileImage} 
                                            alt={profile.Network}
                                            className="w-5 h-5"
                                            onError={(e) => {
                                                e.target.style.display = 'none';
                                            }}
                                        />
                                        <LinkIcon className="w-5 h-5 text-gray-400" />
                                        <a 
                                            href={profile.ProfileLink} 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:text-blue-800"
                                        >
                                            {profile.Network} - {profile.Username}
                                        </a>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                {/* Right Column - Detailed Info */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Summary */}
                    {resume?.summery && (
                        <div className="bg-white border border-gray-200 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Summary</h3>
                            <p className="text-gray-700 leading-relaxed">{resume.summery}</p>
                        </div>
                    )}

                    {/* Skills */}
                    {resume?.Skills && resume.Skills.length > 0 && (
                        <div className="bg-white border border-gray-200 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Skills</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {resume.Skills.map((skill, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <span className="font-medium text-gray-900">{skill.Skill}</span>
                                            <div className="flex flex-wrap gap-1 mt-1">
                                                {skill.Keywords?.map((keyword, kIndex) => (
                                                    <span key={kIndex} className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                                                        {keyword}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                        <span className="text-sm text-gray-600 bg-white px-2 py-1 rounded">
                                            {skill.Proficiency}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Experience */}
                    {resume?.Experience && resume.Experience.length > 0 && (
                        <div className="bg-white border border-gray-200 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Experience</h3>
                            <div className="space-y-4">
                                {resume.Experience.map((exp, index) => (
                                    <div key={index} className="border-l-4 border-blue-500 pl-4">
                                        <div className="flex items-start justify-between">
                                            <div>
                                                <h4 className="font-semibold text-gray-900">{exp.Position}</h4>
                                                <p className="text-blue-600 font-medium">{exp.Company}</p>
                                                <p className="text-gray-600 text-sm">{exp.Location}</p>
                                                {exp.Website && (
                                                    <a 
                                                        href={exp.Website} 
                                                        target="_blank" 
                                                        rel="noopener noreferrer"
                                                        className="text-blue-600 hover:text-blue-800 text-sm"
                                                    >
                                                        {exp.Website}
                                                    </a>
                                                )}
                                            </div>
                                            <span className="text-sm text-gray-500">
                                                {new Date(exp.StartDate).toLocaleDateString()} - {
                                                    exp.EndDate ? new Date(exp.EndDate).toLocaleDateString() : 'Present'
                                                }
                                            </span>
                                        </div>
                                        {exp.Description && (
                                            <p className="text-gray-700 mt-2">{exp.Description}</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Education */}
                    {resume?.Education && resume.Education.length > 0 && (
                        <div className="bg-white border border-gray-200 rounded-xl p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Education</h3>
                            <div className="space-y-4">
                                {resume.Education.map((edu, index) => (
                                    <div key={index} className="border-l-4 border-green-500 pl-4">
                                        <div className="flex items-start justify-between">
                                            <div>
                                                <h4 className="font-semibold text-gray-900">{edu.Degree}</h4>
                                                <p className="text-green-600 font-medium">{edu.Institution}</p>
                                                <p className="text-gray-600 text-sm">{edu.Location}</p>
                                            </div>
                                            <span className="text-sm text-gray-500">
                                                {new Date(edu.StartDate).toLocaleDateString()} - {
                                                    edu.EndDate ? new Date(edu.EndDate).toLocaleDateString() : 'Present'
                                                }
                                            </span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </motion.div>
    );
};

// Resume Tab Component
const ResumeTab = ({ resume }) => {
    return (
        <motion.div
            className="p-6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
        >
            <ResumeTemplate resume={resume} />
        </motion.div>
    );
};

// Test Results Tab Component
const TestResultsTab = ({ testResults }) => {
    return (
        <motion.div
            className="p-6"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
        >
            <div className="bg-white border border-gray-200 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Results</h3>
                {testResults ? (
                    <div className="space-y-4">
                        <div className="flex justify-between">
                            <span className="text-gray-600">Status:</span>
                            <span className="font-medium text-gray-900">
                                {testResults.status?.replace('_', ' ').toUpperCase()}
                            </span>
                        </div>
                        {testResults.score && (
                            <div className="flex justify-between">
                                <span className="text-gray-600">Score:</span>
                                <span className="font-medium text-gray-900">{testResults.score}</span>
                            </div>
                        )}
                    </div>
                ) : (
                    <p className="text-gray-500">No test results available</p>
                )}
            </div>
        </motion.div>
    );
};

export default CandidateDetailsModal;
