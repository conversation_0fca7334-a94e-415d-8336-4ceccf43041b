export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const AUTH_ENDPOINTS = {
    LOGIN: `${API_BASE_URL}/auth/login`,
    REGISTER: `${API_BASE_URL}/auth/register`,
    LOGOUT: `${API_BASE_URL}/auth/logout`,
    CURRENT_USER: `${API_BASE_URL}/auth/me`,
    UPDATE_USER: `${API_BASE_URL}/auth/update`,
    VERIFY_OTP: `${API_BASE_URL}/auth/verify-otp`,
    RESEND_OTP: `${API_BASE_URL}/auth/resend-otp`,
};

export const COMPANY_ENDPOINTS = {
    PROFILE: `${API_BASE_URL}/company/profile`,
    JOBS: `${API_BASE_URL}/company/jobs`,
    DASHBOARD: `${API_BASE_URL}/company/dashboard`,
    JOB_APPLICATIONS: (jobId) => `${API_BASE_URL}/company/jobs/${jobId}/applications`,
    JOB_APPLICATIONS_WITH_RESUMES: (jobId) => `${API_BASE_URL}/company/jobs/${jobId}/applications-with-resumes`,
    JOB_CANDIDATES: (jobId) => `${API_BASE_URL}/company/jobs/${jobId}/candidates`,
    CANDIDATE_DETAILS: (jobId, candidateId) => `${API_BASE_URL}/company/jobs/${jobId}/candidates/${candidateId}`,
    APPLICATIONS_WITH_RESUMES: `${API_BASE_URL}/company/applications-with-resumes`,
    CANDIDATES_ANALYTICS: (jobId) => `${API_BASE_URL}/company/candidates/analytics/${jobId}`,
    JOB_STATUS: (jobId) => `${API_BASE_URL}/company/jobs/${jobId}/status`,
};

export const QUESTION_ENDPOINTS = {
    CREATE: `${API_BASE_URL}/questions`,
    GET_ALL: `${API_BASE_URL}/questions`,
    BY_ID: (id) => `${API_BASE_URL}/questions/${id}`,
    BY_CATEGORY: `${API_BASE_URL}/questions/by-category`,
    CATEGORIES: `${API_BASE_URL}/questions/categories`,
    FILTER: `${API_BASE_URL}/questions/filter`,
    UPLOAD_EXCEL: `${API_BASE_URL}/questions/upload-excel`,
};

export const TEST_ENDPOINTS = {
    CREATE: `${API_BASE_URL}/tests`,
    GET_ALL: `${API_BASE_URL}/tests`,
    BY_ID: (id) => `${API_BASE_URL}/tests/${id}`,
    UPDATE: (id) => `${API_BASE_URL}/tests/${id}`,
    DELETE: (id) => `${API_BASE_URL}/tests/${id}`,
    ASSIGN: (id) => `${API_BASE_URL}/tests/${id}/assign`,
    RESULTS: (id) => `${API_BASE_URL}/tests/${id}/results`,
    ANALYTICS: (id) => `${API_BASE_URL}/tests/${id}/analytics`,
    PARTICIPANT_FEEDBACK: (testId, participantId) =>
        `${API_BASE_URL}/tests/${testId}/participants/${participantId}/feedback`,
    ADD_QUESTIONS: (id) => `${API_BASE_URL}/tests/${id}/questions`,
    REMOVE_QUESTIONS: (id) => `${API_BASE_URL}/tests/${id}/questions`,
    ASSIGN_CANDIDATES: (id) => `${API_BASE_URL}/tests/${id}/assign-candidates`,
    // New endpoints for question management
    QUESTIONS_BY_CATEGORY: `${API_BASE_URL}/tests/questions/by-category`,
    QUESTION_CATEGORIES: `${API_BASE_URL}/tests/questions/categories`,
    FILTER_QUESTIONS: `${API_BASE_URL}/tests/questions/filter`,
    // Question bundles under tests
    QUESTION_BUNDLES: `${API_BASE_URL}/tests/question-bundles`,
    QUESTION_BUNDLE_BY_ID: (id) => `${API_BASE_URL}/tests/question-bundles/${id}`,
    // Candidates under tests
    CANDIDATES: `${API_BASE_URL}/tests/candidates`,
    CANDIDATES_SEARCH: `${API_BASE_URL}/tests/candidates/search`,
    CANDIDATES_AVAILABLE_FOR_TEST: `${API_BASE_URL}/tests/candidates/available-for-test`,
};

export const CANDIDATE_ENDPOINTS = {
    GET_ALL: `${API_BASE_URL}/candidates`,
    SEARCH: `${API_BASE_URL}/candidates/search`,
    BY_JOB: (jobId) => `${API_BASE_URL}/candidates/job/${jobId}`,
    AVAILABLE_FOR_TEST: `${API_BASE_URL}/candidates/available-for-test`,
};

export const QUESTION_BUNDLE_ENDPOINTS = {
    CREATE: `${API_BASE_URL}/tests/question-bundles`,
    GET_ALL: `${API_BASE_URL}/tests/question-bundles`,
    BY_ID: (id) => `${API_BASE_URL}/tests/question-bundles/${id}`,
    UPDATE: (id) => `${API_BASE_URL}/tests/question-bundles/${id}`,
    DELETE: (id) => `${API_BASE_URL}/tests/question-bundles/${id}`,
};

export const ADMIN_ENDPOINTS = {
    USERS: `${API_BASE_URL}/admin/users`,
    USER: (userId) => `${API_BASE_URL}/admin/users/${userId}`,
    ACTIVATE_USER: (userId) => `${API_BASE_URL}/admin/users/${userId}/activate`,
    COMPANIES: `${API_BASE_URL}/admin/companies`,
    PENDING_COMPANIES: `${API_BASE_URL}/admin/companies/pending`,
    COMPANY: (companyId) => `${API_BASE_URL}/admin/companies/${companyId}`,
    JOBS: `${API_BASE_URL}/admin/jobs`,
    JOB: (jobId) => `${API_BASE_URL}/admin/jobs/${jobId}`,
    FLAG_JOB: (jobId) => `${API_BASE_URL}/admin/jobs/${jobId}/flag`,
    TESTS: `${API_BASE_URL}/admin/tests`,
    TEST: (testId) => `${API_BASE_URL}/admin/tests/${testId}`,
    FLAG_TEST: (testId) => `${API_BASE_URL}/admin/tests/${testId}/flag`,
    SETTINGS: `${API_BASE_URL}/admin/settings`
};


export const STUDENT_ENDPOINTS = {
    PROFILE: `${API_BASE_URL}/student/profile`,
    JOBS: `${API_BASE_URL}/student/jobs`,
    JOB_DETAILS: (jobId) => `${API_BASE_URL}/student/jobs/${jobId}`,
    APPLY_TO_JOB: (jobId) => `${API_BASE_URL}/student/jobs/${jobId}/apply`,
    APPLICATIONS: `${API_BASE_URL}/student/applications`,
    TESTS: `${API_BASE_URL}/student/tests`,
    UPCOMING_TESTS: `${API_BASE_URL}/student/tests/upcoming`,
    LIVE_TESTS: `${API_BASE_URL}/student/tests/live`,
    TEST_DETAILS: (testId) => `${API_BASE_URL}/student/tests/${testId}`,
    TEST_HISTORY: `${API_BASE_URL}/student/tests/history`,
    START_TEST: (testId) => `${API_BASE_URL}/student/tests/${testId}/start`,
    SUBMIT_TEST: (testId) => `${API_BASE_URL}/student/tests/${testId}/submit`,
    RESULTS: `${API_BASE_URL}/student/results`,
    RESULT_DETAILS: (resultId) => `${API_BASE_URL}/student/results/${resultId}`,
    APPLICATION_DETAILS: (applicationId) => `${API_BASE_URL}/student/applications/${applicationId}`,
};
