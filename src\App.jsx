import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { Toaster } from 'react-hot-toast';
import './styles/layout.css';

// General Components
import Navbar from "./Components/Navbar";
import Hero from "./Components/Hero";
import AnimatedCardsSection from "./Components/AnimatedCardsSection";
import About from "./Components/About";
import HowItWorks from "./Components/HowItWorks";
import Testimonials from "./Components/Testimonials";
import Footer from "./Components/Footer";
import Layout from "./Components/Layout";

// Protected Route Components
import ProtectedRoute from "./Components/auth/ProtectedRoute";
import { AdminRoute, CompanyRoute, StudentRoute } from "./Components/auth/RoleBasedRoute";
import RoleBasedRedirect from "./Components/auth/RoleBasedRedirect";

// Company Components
import CompanyDashboard from "./Components/company/CompanyDashboard";
import CreateJob from './Components/company/CreateJob';
import TestManagement from "./Components/company/TestManagement";
import Aptitude from "./Components/company/Aptitude";
import Interview from "./Components/company/Interview";
import CompanyProfile from "./Components/company/Profile";

// Admin Components
import AdminDashboard from "./Components/admin/AdminDashboard";
import AdminJobPosts from "./Components/admin/AdminJobPosts";
import AdminCompanies from "./Components/admin/AdminCompanies";
import AdminUsers from "./Components/admin/AdminUsers";
import AdminSettings from "./Components/admin/AdminSettings";

// Student Components - Legacy
import StudentDashboard from "./Components/Dashboard/Studentdashboard";
import TestInterface from "./Components/company/components/TestInterface";
import TestResult from "./Components/Dashboard/Quiz";
import InterviewPrep from "./Components/Dashboard/Interview";

// Student Components - New Layout
import StudentLayout from "./pages/student/components/StudentLayout";
import StudentHome from "./pages/student/Home";
import StudentProfile from "./pages/student/Profile";
import StudentJobs from "./pages/student/Jobs";
import StudentApplications from "./pages/student/Applications";
import StudentTests from "./pages/student/Tests";
import StudentTestDetails from "./pages/student/TestDetails";
import StudentResults from "./pages/student/Results";
import JobDetails from "./pages/student/JobDetails";
import JobsCategory from "./pages/student/JobsCategory";
import ApplicationDetails from "./pages/student/applications/ApplicationDetails";
import Courses from "./pages/student/applications/Courses";
import Certificates from "./pages/student/applications/Certificates";
import Resume from "./pages/student/Resume";

// Demo Components
import AuthDemo from "./Components/demo/AuthDemo";

// Auth Pages
import RegistrationPage from "./pages/RegistationPage";
import VerifyOtp from "./pages/VerifyOtp";
import LoginPage from "./pages/LoginPage";

function LandingPage() {
  return (
    <div className="font-sans text-gray-800 bg-[#f7f8fa]">
      <Navbar />
      <main>
        <section className="min-h-[85vh] flex items-center justify-center bg-white">
          <Hero />
        </section>
        <section className="py-16 px-4 bg-white">
          <div className="max-w-7xl mx-auto bg-white">
            <AnimatedCardsSection />
          </div>
        </section>
        <section className="py-20 px-4 bg-white">
          <About />
        </section>
        <section className="py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <HowItWorks />
          </div>
        </section>
        <section className="py-20 px-4">
          <Testimonials dark={false} />
        </section>
      </main>
      <Footer />
    </div>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<LandingPage />} />

        {/* Role-based dashboard redirect for authenticated users */}
        <Route path="/dashboard-redirect" element={<RoleBasedRedirect />} />

        {/* Demo Route - For testing authentication */}
        <Route path="/auth-demo" element={<AuthDemo />} />

        {/* Auth Routes - Redirect to dashboard if already logged in */}
        <Route
          path="/register"
          element={
            <ProtectedRoute requireAuth={false}>
              <RegistrationPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/verify-otp"
          element={
            <ProtectedRoute requireAuth={false}>
              <VerifyOtp />
            </ProtectedRoute>
          }
        />
        <Route
          path="/login"
          element={
            <ProtectedRoute requireAuth={false}>
              <LoginPage />
            </ProtectedRoute>
          }
        />

        {/* Admin Routes - Only accessible by admin role */}
        <Route
          path="/admin-dashboard"
          element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          }
        >
          <Route path="users" element={<AdminUsers />} />
          <Route path="companies" element={<AdminCompanies />} />
          <Route path="job-posts" element={<AdminJobPosts />} />
          <Route path="settings" element={<AdminSettings />} />
        </Route>

        {/* Legacy Admin Routes - Redirect to nested routes */}
        <Route path="/admin-dashboard/users" element={<Navigate to="/admin-dashboard/users" replace />} />
        <Route path="/admin-dashboard/companies" element={<Navigate to="/admin-dashboard/companies" replace />} />
        <Route path="/admin-dashboard/job-posts" element={<Navigate to="/admin-dashboard/job-posts" replace />} />
        <Route path="/admin-dashboard/settings" element={<Navigate to="/admin-dashboard/settings" replace />} />

        {/* Company Routes under Layout - Only accessible by company role */}
        <Route
          path="/"
          element={
            <CompanyRoute>
              <Layout />
            </CompanyRoute>
          }
        >
          <Route path="dashboard" element={<CompanyDashboard />} />
          <Route path="job-create" element={<CreateJob />} />
          <Route path="test-management" element={<TestManagement />} />
          <Route path="aptitude" element={<Aptitude />} />
          <Route path="interview" element={<Interview />} />
          <Route path="profile" element={<CompanyProfile />} />
        </Route>

        {/* Legacy Company Routes - Redirect to new structure */}
        <Route path="/dashboard/job-create" element={<Navigate to="/job-create" replace />} />
        <Route path="/dashboard/aptitude" element={<Navigate to="/aptitude" replace />} />
        <Route path="/dashboard/test" element={<Navigate to="/test-management" replace />} />
        <Route path="/dashboard/interview" element={<Navigate to="/interview" replace />} />
        <Route path="/dashboard/profile" element={<Navigate to="/profile" replace />} />

        {/* Student Routes - New Layout Structure */}
        <Route
          path="/student"
          element={
            <StudentRoute>
              <StudentLayout />
            </StudentRoute>
          }
        >
          <Route index element={<StudentHome />} />
          <Route path="home" element={<StudentHome />} />
          <Route path="profile" element={<StudentProfile />} />
          <Route path="resume" element={<Resume />} />
          <Route path="jobs" element={<StudentJobs />} />
          <Route path="jobs/category/:categoryName" element={<JobsCategory />} />
          <Route path="jobs/:id" element={<JobDetails />} />
          <Route path="applications" element={<StudentApplications />} />
          <Route path="applications/:id" element={<ApplicationDetails />} />
          <Route path="tests" element={<StudentTests />} />
          <Route path="tests/:testid" element={<StudentTestDetails />} />
          <Route path="results" element={<StudentResults />} />
          <Route path="courses" element={<Courses />} />
          <Route path="certificates" element={<Certificates />} />
        </Route>

        {/* Legacy Student Routes - Redirect to new structure */}
        <Route path="/student-dashboard" element={<Navigate to="/student/home" replace />} />
        <Route path="/student-profile" element={<Navigate to="/student/profile" replace />} />

        {/* Legacy Student Test Routes - For backward compatibility */}
        <Route
          path="/test"
          element={
            <StudentRoute>
              <TestInterface />
            </StudentRoute>
          }
        />
        <Route
          path="/test-result"
          element={
            <StudentRoute>
              <TestResult />
            </StudentRoute>
          }
        />
        <Route
          path="/interview-prep"
          element={
            <StudentRoute>
              <InterviewPrep />
            </StudentRoute>
          }
        />

        {/* Alternative Student Dashboard - Legacy Support */}
        <Route
          path="/legacy-student-dashboard"
          element={
            <StudentRoute>
              <StudentDashboard />
            </StudentRoute>
          }
        />

        {/* Catch all route - redirect to home */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
            borderRadius: '10px',
            padding: '16px',
            fontSize: '14px',
            fontWeight: '500',
          },
          success: {
            style: { background: '#10B981' },
            iconTheme: { primary: '#fff', secondary: '#10B981' },
          },
          error: {
            style: { background: '#EF4444' },
            iconTheme: { primary: '#fff', secondary: '#EF4444' },
          },
          loading: {
            style: { background: '#3B82F6' },
          },
        }}
      />
    </Router>
  );
}

export default App;