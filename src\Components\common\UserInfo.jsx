import React from 'react';
import { User, Crown, Building, GraduationCap, CrownIcon, UtensilsCrossedIcon, UserCircle2Icon, } from 'lucide-react';
import { FaUserCircle } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';

// /**
//  * Reusable User Info Component for sidebars and headers
//  * @param {Object} props
//  * @param {string} props.variant - Display variant ('sidebar', 'header', 'compact')
//  * @param {string} props.className - Additional CSS classes
//  * @param {boolean} props.showRole - Whether to show user role
//  * @param {boolean} props.showEmail - Whether to show user email
//  * @param {string} props.iconType - Icon type ('lucide' or 'fa')
//  */
const UserInfo = ({
  variant = 'sidebar',
  className = '',
  showRole = true,
  showEmail = true,
  iconType = 'lucide'
}) => {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return null;
  }

  // Get role-specific icon and color
  const getRoleIcon = () => {
    switch (user.role) {
      case 'admin':
        return { icon: UserCircle2Icon, color: 'text-yellow-200' };
      case 'company':
        return { icon: Building, color: 'text-blue-400' };
      case 'student':
        return { icon: GraduationCap, color: 'text-green-400' };
      default:
        return { icon: User, color: 'text-gray-400' };
    }
  };

  const { icon: RoleIcon, color: roleColor } = getRoleIcon();

  // Get variant-specific styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'header':
        return {
          container: 'flex items-center gap-3',
          avatar: 'w-8 h-8',
          name: 'text-sm font-medium',
          email: 'text-xs opacity-75',
          role: 'text-xs opacity-75 capitalize'
        };
      
      case 'compact':
        return {
          container: 'flex items-center gap-2',
          avatar: 'w-6 h-6',
          name: 'text-sm font-medium',
          email: 'text-xs opacity-75',
          role: 'text-xs opacity-75 capitalize'
        };
      
      case 'sidebar':
      default:
        return {
          container: 'flex items-center gap-3 text-white',
          avatar: 'w-10 h-10',
          name: 'text-lg font-bold tracking-tight',
          email: 'text-sm text-gray-300',
          role: 'text-xs text-gray-400 capitalize'
        };
    }
  };

  const styles = getVariantStyles();

  // Avatar component
  const AvatarComponent = () => {
    if (iconType === 'fa') {
      return <FaUserCircle className={`${styles.avatar} text-blue-400`} />;
    }

    return (
      <div className={`${styles.avatar} bg-white/10 rounded-xl flex items-center justify-center`}>
        <RoleIcon className={`w-6 h-6 ${roleColor}`} />
      </div>
    );
  };

  // Get display name
  const displayName = user.name || user.email?.split('@')[0] || 'User';
  
  // Get role display text
  const getRoleDisplayText = () => {
    switch (user.role) {
      case 'admin':
        return 'Administrator';
      case 'company':
        return 'Company User';
      case 'student':
        return 'Student';
      default:
        return user.role || 'User';
    }
  };

  return (
    <div className={`${styles.container} ${className}`}>
      <AvatarComponent />
      
      <div className="flex-1 min-w-0">
        <h3 className={`${styles.name} truncate`}>
          {displayName}
        </h3>
        
        {showEmail && user.email && (
          <p className={`${styles.email} truncate`}>
            {user.email}
          </p>
        )}
        
        {showRole && (
          <p className={`${styles.role} flex items-center gap-1`}>
            <RoleIcon className="w-3 h-3" />
            {getRoleDisplayText()}
          </p>
        )}
      </div>
    </div>
  );
};

/**
 * Sidebar-specific user info
 */
export const SidebarUserInfo = (props) => (
  <UserInfo variant="sidebar" {...props} />
);

/**
 * Header-specific user info
 */
export const HeaderUserInfo = (props) => (
  <UserInfo variant="header" {...props} />
);

/**
 * Compact user info
 */
export const CompactUserInfo = (props) => (
  <UserInfo variant="compact" {...props} />
);

export default UserInfo;
