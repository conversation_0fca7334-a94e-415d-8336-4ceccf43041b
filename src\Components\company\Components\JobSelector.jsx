import React from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeftIcon, 
  BriefcaseIcon,
  ClockIcon,
  DocumentTextIcon 
} from '@heroicons/react/24/outline';

const JobSelector = ({ 
  jobs = [], 
  selectedJobIndex, 
  onJobSelect, 
  onBack,
  getQuestionsCount,
  className = ""
}) => {
  const selectedJob = jobs[selectedJobIndex];

  return (
    <aside className={`fixed top-16 left-0 z-40 h-[calc(100vh-4rem)] w-80 bg-gradient-to-b from-[rgb(35,65,75)] to-gray-900 border-r border-gray-900 shadow-2xl flex flex-col p-0 ${className}`}>
      {/* Back Button */}
      <button
        className="absolute top-4 right-4 flex items-center gap-2 px-3 py-2 bg-white/80 hover:bg-gray-200 text-gray-800 rounded-full shadow transition-all duration-200 font-semibold text-base z-50"
        onClick={onBack}
      >
        <ArrowLeftIcon className="w-4 h-4" />
        Back
      </button>

      {/* Header */}
      <div className="px-8 py-7 border-b border-gray-800">
        <div className="flex items-center gap-3 text-2xl font-extrabold text-white tracking-tight">
          <BriefcaseIcon className="w-7 h-7 text-white opacity-80" />
          Jobs
        </div>
        {selectedJob && (
          <div className="mt-3 text-sm text-gray-300">
            Selected: <span className="text-white font-semibold">{selectedJob.title}</span>
          </div>
        )}
      </div>

      {/* Job List */}
      <nav className="flex-1 overflow-y-auto custom-scrollbar px-4 py-6">
        {jobs.length === 0 ? (
          <div className="text-gray-400 text-center mt-10 flex flex-col items-center gap-4">
            <div className="w-16 h-16 bg-gray-700/30 rounded-full flex items-center justify-center">
              <BriefcaseIcon className="w-8 h-8 text-gray-600" />
            </div>
            <div>
              <div className="text-base font-medium mb-1">No Jobs Available</div>
              <div className="text-sm text-gray-500">Create some jobs first to add questions</div>
            </div>
          </div>
        ) : (
          <ul className="flex flex-col gap-3">
            {jobs.map((job, index) => {
              const isSelected = selectedJobIndex === index;
              const questionsCount = getQuestionsCount ? getQuestionsCount(job.title) : 0;
              
              return (
                <motion.li 
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <button
                    className={`w-full text-left px-6 py-4 rounded-xl transition-all duration-200 flex flex-col gap-2 shadow-sm group
                      ${isSelected
                        ? 'bg-white/10 text-white font-bold shadow-lg ring-2 ring-blue-400/40 scale-[1.02]'
                        : 'text-gray-200 hover:bg-white/5 hover:text-white hover:scale-[1.01]'}
                    `}
                    onClick={() => onJobSelect(index)}
                  >
                    {/* Job Title */}
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        isSelected ? 'bg-blue-400' : 'bg-gray-500 group-hover:bg-gray-400'
                      }`} />
                      <span className="truncate text-lg font-semibold">
                        {job.title || 'Untitled Job'}
                      </span>
                    </div>

                    {/* Job Details */}
                    <div className="ml-6 space-y-1">
                      {job.location && (
                        <div className="flex items-center gap-2 text-xs text-gray-400">
                          <span>📍</span>
                          <span className="truncate">{job.location}</span>
                        </div>
                      )}
                      
                      {job.jobType && (
                        <div className="flex items-center gap-2 text-xs text-gray-400">
                          <ClockIcon className="w-3 h-3" />
                          <span>{job.jobType}</span>
                        </div>
                      )}

                      {/* Questions Count */}
                      <div className="flex items-center gap-2 text-xs">
                        <DocumentTextIcon className="w-3 h-3" />
                        <span className={questionsCount > 0 ? 'text-green-400' : 'text-gray-400'}>
                          {questionsCount} question{questionsCount !== 1 ? 's' : ''}
                        </span>
                      </div>

                      {/* Job Status */}
                      {job.isActive !== undefined && (
                        <div className="flex items-center gap-2 text-xs">
                          <div className={`w-2 h-2 rounded-full ${
                            job.isActive ? 'bg-green-400' : 'bg-red-400'
                          }`} />
                          <span className={job.isActive ? 'text-green-400' : 'text-red-400'}>
                            {job.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Selection Indicator */}
                    {isSelected && (
                      <motion.div
                        className="absolute right-2 top-1/2 transform -translate-y-1/2"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                      >
                        <div className="w-2 h-2 bg-blue-400 rounded-full" />
                      </motion.div>
                    )}
                  </button>
                </motion.li>
              );
            })}
          </ul>
        )}
      </nav>

      {/* Footer Stats */}
      {jobs.length > 0 && (
        <div className="px-6 py-4 border-t border-gray-800 bg-gray-900/50">
          <div className="text-center text-sm text-gray-400">
            <div className="font-semibold text-white">{jobs.length}</div>
            <div>Total Job{jobs.length !== 1 ? 's' : ''}</div>
          </div>
          
          {selectedJob && getQuestionsCount && (
            <div className="mt-3 text-center text-sm">
              <div className="font-semibold text-blue-400">
                {getQuestionsCount(selectedJob.title)}
              </div>
              <div className="text-gray-400">Questions Available</div>
            </div>
          )}
        </div>
      )}

      {/* Custom Scrollbar Styles */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.3);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.5);
        }
      `}</style>
    </aside>
  );
};

export default JobSelector;
