import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  UserGroupIcon,
  ClockIcon,
  ChartBarIcon,
  DocumentTextIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

// Components
import TestCreationWizard from './components/TestCreationWizard';
import TestDetailsModal from './components/TestDetailsModal';
import TestResultsModal from './components/TestResultsModal';
import CandidateSelectionModal from './components/CandidateSelectionModal';
import AddQuestionsToTestModal from './components/AddQuestionsToTestModal';

// Hooks and Store
import useCompanyStore from '../../store/companyStore';

const TestManagement = () => {
  const navigate = useNavigate();
  const {
    tests,
    loading,
    error,
    getTests,
    createTest,
    updateTest,
    deleteTest,
    getTestResults,
    getTestAnalytics,
    questions,
    getQuestions,
    jobs,
    getJobs,
    assignCandidatesToTest
  } = useCompanyStore();

  // Modal states
  const [showCreateWizard, setShowCreateWizard] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showResultsModal, setShowResultsModal] = useState(false);
  const [showCandidateModal, setShowCandidateModal] = useState(false);
  const [showAddQuestionsModal, setShowAddQuestionsModal] = useState(false);
  const [editingTest, setEditingTest] = useState(null);
  const [selectedTest, setSelectedTest] = useState(null);

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all'); // all, active, inactive, completed
  const [sortBy, setSortBy] = useState('createdAt'); // createdAt, testName, scheduledDate

  useEffect(() => {
    const initializeData = async () => {
      await Promise.all([
        getTests(),
        getQuestions(),
        getJobs()
      ]);
    };
    initializeData();
  }, [getTests, getQuestions, getJobs]);

  // Filter and sort tests
  const filteredTests = tests
    .filter(test => {
      const matchesSearch = test.testName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        test.description?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'active' && test.isActive) ||
        (statusFilter === 'inactive' && !test.isActive) ||
        (statusFilter === 'completed' && new Date(test.endDate) < new Date());

      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'testName':
          return a.testName.localeCompare(b.testName);
        case 'scheduledDate':
          return new Date(a.scheduledDate) - new Date(b.scheduledDate);
        default:
          return new Date(b.createdAt) - new Date(a.createdAt);
      }
    });

  const handleCreateTest = async (testData) => {
    const result = await createTest(testData);
    if (result) {
      toast.success('Test created successfully');
      setShowCreateWizard(false);
      await getTests(); // Refresh tests
      return { success: true };
    }
    return { success: false, error: 'Failed to create test' };
  };

  const handleUpdateTest = async (testData) => {
    if (!editingTest) return { success: false, error: 'No test selected for editing' };

    const result = await updateTest(editingTest._id, testData);
    if (result) {
      toast.success('Test updated successfully');
      setShowCreateWizard(false);
      setEditingTest(null);
      await getTests(); // Refresh tests
      return { success: true };
    }
    return { success: false, error: 'Failed to update test' };
  };

  const handleDeleteTest = async (testId, testName) => {
    if (window.confirm(`Are you sure you want to delete "${testName}"? This action cannot be undone.`)) {
      const result = await deleteTest(testId);
      if (result?.success) {
        toast.success('Test deleted successfully');
      } else {
        toast.error('Failed to delete test');
      }
    }
  };

  const handleEditTest = (test) => {
    setEditingTest(test);
    setShowCreateWizard(true);
  };

  const handleViewDetails = (test) => {
    setSelectedTest(test);
    setShowDetailsModal(true);
  };

  const handleViewResults = (test) => {
    setSelectedTest(test);
    setShowResultsModal(true);
  };

  const handleAssignCandidates = (test) => {
    setSelectedTest(test);
    setShowCandidateModal(true);
  };

  const handleAddQuestions = (test) => {
    setSelectedTest(test);
    setShowAddQuestionsModal(true);
  };

  const handleCandidateAssignment = async (candidateIds) => {
    if (!selectedTest) return;

    const result = await assignCandidatesToTest(selectedTest._id, candidateIds);
    if (result) {
      toast.success(`Assigned ${candidateIds.length} candidates to test`);
      await getTests(); // Refresh tests
    } else {
      toast.error('Failed to assign candidates');
    }
  };

  const handleQuestionsAdded = async (addedQuestions) => {
    toast.success(`Added ${addedQuestions.length} questions to test`);
    await getTests(); // Refresh tests
  };

  const getTestStatus = (test) => {
    const now = new Date();
    const startDate = new Date(test.scheduledDate);
    const endDate = new Date(test.endDate);

    if (!test.isActive) return { status: 'inactive', color: 'gray', text: 'Inactive' };
    if (now < startDate) return { status: 'scheduled', color: 'blue', text: 'Scheduled' };
    if (now >= startDate && now <= endDate) return { status: 'active', color: 'green', text: 'Active' };
    if (now > endDate) return { status: 'completed', color: 'purple', text: 'Completed' };

    return { status: 'unknown', color: 'gray', text: 'Unknown' };
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl font-semibold text-gray-600">Loading tests...</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-[rgb(35,65,75)] to-gray-700 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Test Management</h1>
              <p className="text-sm text-gray-600">Create, manage, and monitor your company tests</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowCreateWizard(true)}
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[rgb(35,65,75)] to-gray-700 hover:from-[rgb(35,65,75)]/90 hover:to-gray-700/90 text-white rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
            >
              <PlusIcon className="h-5 w-5" />
              Create Test
            </button>
          </div>
        </div>
      </div>
      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 items-center w-full lg:w-auto">
            <div className="relative w-full sm:w-auto">
              <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <input
                type="text"
                placeholder="Search tests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full sm:w-64 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[rgb(35,65,75)] focus:border-transparent"
              />
            </div>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[rgb(35,65,75)] focus:border-transparent w-full sm:w-auto"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="completed">Completed</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[rgb(35,65,75)] focus:border-transparent w-full sm:w-auto"
            >
              <option value="createdAt">Sort by Created Date</option>
              <option value="testName">Sort by Name</option>
              <option value="scheduledDate">Sort by Schedule</option>
            </select>
          </div>

          <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-lg">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span className="font-medium">{filteredTests.length}</span> of {tests.length} tests
          </div>
        </div>
      </div>

      {/* Tests Grid */}
      <div className="space-y-6">
        {filteredTests.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <DocumentTextIcon className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {tests.length === 0 ? 'No tests created yet' : 'No tests match your filters'}
            </h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              {tests.length === 0
                ? 'Create your first test to get started with candidate assessment and streamline your hiring process'
                : 'Try adjusting your search or filter criteria to find the tests you\'re looking for'
              }
            </p>
            {tests.length === 0 && (
              <button
                onClick={() => setShowCreateWizard(true)}
                className="px-8 py-3 bg-gradient-to-r from-[rgb(35,65,75)] to-gray-700 hover:from-[rgb(35,65,75)]/90 hover:to-gray-700/90 text-white rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Create Your First Test
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTests.map((test, index) => {
              const status = getTestStatus(test);
              const participantCount = test.participants?.length || 0;
              const completedCount = test.participants?.filter(p => p.status === 'completed').length || 0;

              return (
                <motion.div
                  key={test._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-shadow"
                >
                  {/* Test Header */}
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-lg font-bold text-gray-800 line-clamp-2">
                        {test.testName}
                      </h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full bg-${status.color}-100 text-${status.color}-700`}>
                        {status.text}
                      </span>
                    </div>

                    {test.description && (
                      <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                        {test.description}
                      </p>
                    )}

                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <ClockIcon className="h-4 w-4" />
                        {test.duration} min
                      </div>
                      <div className="flex items-center gap-1">
                        <DocumentTextIcon className="h-4 w-4" />
                        {test.questions?.length || 0} questions
                      </div>
                    </div>
                  </div>

                  {/* Test Stats */}
                  <div className="p-4 bg-gray-50">
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-lg font-bold text-gray-800">{participantCount}</div>
                        <div className="text-xs text-gray-600">Assigned</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-green-600">{completedCount}</div>
                        <div className="text-xs text-gray-600">Completed</div>
                      </div>
                    </div>
                  </div>

                  {/* Test Dates */}
                  <div className="p-4 border-t border-gray-100">
                    <div className="text-xs text-gray-500 space-y-1">
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="h-3 w-3" />
                        Start: {formatDate(test.scheduledDate)}
                      </div>
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="h-3 w-3" />
                        End: {formatDate(test.endDate)}
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="p-4 border-t border-gray-100">
                    <div className="grid grid-cols-2 gap-2 mb-3">
                      <button
                        onClick={() => handleViewDetails(test)}
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="View Details"
                      >
                        <EyeIcon className="h-4 w-4" />
                        Details
                      </button>

                      <button
                        onClick={() => handleViewResults(test)}
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                        title="View Results"
                      >
                        <ChartBarIcon className="h-4 w-4" />
                        Results
                      </button>

                      <button
                        onClick={() => handleAssignCandidates(test)}
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
                        title="Assign Candidates"
                      >
                        <UserGroupIcon className="h-4 w-4" />
                        Assign
                      </button>

                      <button
                        onClick={() => handleAddQuestions(test)}
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors"
                        title="Add Questions"
                      >
                        <PlusIcon className="h-4 w-4" />
                        Questions
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <button
                        onClick={() => handleEditTest(test)}
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Edit Test"
                      >
                        <PencilIcon className="h-4 w-4" />
                        Edit
                      </button>

                      <button
                        onClick={() => handleDeleteTest(test._id, test.testName)}
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete Test"
                      >
                        <TrashIcon className="h-4 w-4" />
                        Delete
                      </button>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        )}
      </div>

      {/* Modals */}
      <TestCreationWizard
        isOpen={showCreateWizard}
        onClose={() => {
          setShowCreateWizard(false);
          setEditingTest(null);
        }}
        onComplete={editingTest ? handleUpdateTest : handleCreateTest}
        editingTest={editingTest}
        jobs={jobs}
        questions={questions}
      />

      <TestDetailsModal
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedTest(null);
        }}
        test={selectedTest}
      />

      <TestResultsModal
        isOpen={showResultsModal}
        onClose={() => {
          setShowResultsModal(false);
          setSelectedTest(null);
        }}
        test={selectedTest}
      />

      <CandidateSelectionModal
        isOpen={showCandidateModal}
        onClose={() => {
          setShowCandidateModal(false);
          setSelectedTest(null);
        }}
        onAssign={handleCandidateAssignment}
        testId={selectedTest?._id}
        alreadyAssignedCandidates={selectedTest?.participants?.map(p => p.candidateId) || []}
      />

      <AddQuestionsToTestModal
        isOpen={showAddQuestionsModal}
        onClose={() => {
          setShowAddQuestionsModal(false);
          setSelectedTest(null);
        }}
        test={selectedTest}
        onQuestionsAdded={handleQuestionsAdded}
      />

      {/* Error Display */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50">
          <span className="block sm:inline">{error}</span>
          <button
            onClick={() => useCompanyStore.getState().clearError()}
            className="ml-2 text-red-700 hover:text-red-900"
          >
            ×
          </button>
        </div>
      )}
    </div>
  );
};

export default TestManagement;
