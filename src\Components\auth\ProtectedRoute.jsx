import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import useAuthStore from '../../store/authStore';

/**
 * ProtectedRoute component that handles authentication-based route protection
 * @param {Object} props
 * @param {React.ReactNode} props.children - The component to render if authenticated
 * @param {string} props.redirectTo - Where to redirect if not authenticated (default: '/login')
 * @param {boolean} props.requireAuth - Whether authentication is required (default: true)
 */
const ProtectedRoute = ({ 
  children, 
  redirectTo = '/login', 
  requireAuth = true 
}) => {
  const { user, isAuthenticated, loading, initialized, initialize } = useAuthStore();
  const location = useLocation();

  useEffect(() => {
    if (!initialized) {
      initialize();
    }
  }, [initialized, initialize]);

  // Show loading spinner while checking authentication
  if (!initialized || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-200">
        <div className="text-center">
          <DotLottieReact
            src="https://lottie.host/454c3626-2618-4344-b957-5f9c8d674a99/UVood7R6b1.lottie"
            loop
            autoplay
            style={{ width: 200, height: 200 }}
          />
          <p className="mt-4 text-gray-600 font-medium">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    // Save the attempted location for redirect after login
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // If authentication is not required but user is authenticated,
  // we might want to redirect them to their dashboard
  if (!requireAuth && isAuthenticated && user) {
    // This is for public routes that authenticated users shouldn't see (like login page)
    const dashboardPath = getDashboardPath(user.role);
    if (location.pathname === '/login' || location.pathname === '/register' || location.pathname === '/verify-otp') {
      return <Navigate to={dashboardPath} replace />;
    }
  }

  return children;
};

/**
 * Get the appropriate dashboard path based on user role
 * @param {string} role - User role (admin, company, student)
 * @returns {string} Dashboard path
 */
const getDashboardPath = (role) => {
  switch (role) {
    case 'admin':
      return '/admin-dashboard';
    case 'company':
      return '/dashboard';
    case 'student':
    case 'candidate':
      return '/student/home';
    default:
      return '/';
  }
};

export default ProtectedRoute;
